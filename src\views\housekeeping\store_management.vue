<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">店铺管理</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">店铺状态</span>
            <el-select v-model="selectedStatus" placeholder="选择状态" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option label="营业中" value="active" />
              <el-option label="已停业" value="inactive" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">区域</span>
            <el-select v-model="selectedRegion" placeholder="选择区域" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="region in regionList"
                :key="region.id"
                :label="region.name"
                :value="region.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">关键词</span>
            <el-input v-model="keyword" placeholder="店铺名称/地址" class="w-[200px]" />
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap" @click="openCreateDialog">
              新增店铺
            </el-button>
            <el-button type="info" class="!rounded-button whitespace-nowrap" @click="exportData">
              导出
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" class="w-full">
        <el-table-column width="80">
          <template #default="scope">
            <el-image
              :src="scope.row.logo || defaultLogo"
              :preview-src-list="scope.row.logo ? [scope.row.logo] : []"
              fit="cover"
              class="w-10 h-10 rounded-md"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="店铺名称" width="180" />
        <el-table-column prop="address" label="地址" width="250" show-overflow-tooltip />
        <el-table-column prop="regionName" label="区域" width="120" />
        <el-table-column prop="phone" label="联系电话" width="120" />
        <el-table-column prop="managerName" label="店长" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '营业中' : '已停业' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="openTime" label="营业时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看</el-button>
            <el-button type="warning" link @click="editStore(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="confirmDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 店铺详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="店铺详情" width="800px">
      <div v-if="detailLoading" class="flex justify-center items-center py-20">
        <el-spinner size="large" />
      </div>
      <div v-else class="flex">
        <div class="w-1/3 flex flex-col items-center">
          <el-image
            :src="storeDetail.logo || defaultLogo"
            :preview-src-list="storeDetail.logo ? [storeDetail.logo] : []"
            fit="cover"
            class="w-32 h-32 rounded-md mb-4"
          />
          <h3 class="text-xl font-bold mb-2">{{ storeDetail.name }}</h3>
          <p class="text-gray-500">
            <el-tag :type="storeDetail.status === 'active' ? 'success' : 'danger'">
              {{ storeDetail.status === 'active' ? '营业中' : '已停业' }}
            </el-tag>
          </p>
        </div>
        <div class="w-2/3">
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">地址</span>
              <span class="font-medium">{{ storeDetail.address }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">区域</span>
              <span class="font-medium">{{ storeDetail.regionName }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">联系电话</span>
              <span class="font-medium">{{ storeDetail.phone }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">店长</span>
              <span class="font-medium">{{ storeDetail.managerName }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">营业时间</span>
              <span class="font-medium">{{ storeDetail.openTime }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">创建时间</span>
              <span class="font-medium">{{ storeDetail.createTime }}</span>
            </div>
          </div>

          <div class="mb-6">
            <span class="text-gray-500 text-sm">店铺简介</span>
            <div class="mt-1 p-3 border rounded-lg">
              {{ storeDetail.description || '无' }}
            </div>
          </div>

          <div v-if="storeDetail.images && storeDetail.images.length > 0" class="mb-6">
            <span class="text-gray-500 text-sm">店铺图片</span>
            <div class="mt-1 grid grid-cols-4 gap-4">
              <div v-for="(image, index) in storeDetail.images" :key="index" class="border rounded-lg p-2">
                <el-image
                  :src="image.url"
                  :preview-src-list="storeDetail.images.map(img => img.url)"
                  fit="cover"
                  class="w-full h-24 object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑店铺对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑店铺信息' : '新增店铺'"
      width="800px"
    >
      <el-form
        ref="storeFormRef"
        :model="storeForm"
        :rules="storeRules"
        label-width="100px"
      >
        <div class="flex mb-6">
          <div class="w-1/3 flex flex-col items-center">
            <el-image
              :src="logoUrl || defaultLogo"
              fit="cover"
              class="w-32 h-32 rounded-md mb-4"
            />
            <el-upload
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleLogoChange"
            >
              <el-button type="primary">上传Logo</el-button>
            </el-upload>
          </div>
          <div class="w-2/3">
            <el-form-item label="店铺名称" prop="name">
              <el-input v-model="storeForm.name" placeholder="请输入店铺名称" />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="storeForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="店长" prop="managerName">
              <el-input v-model="storeForm.managerName" placeholder="请输入店长姓名" />
            </el-form-item>
          </div>
        </div>

        <el-form-item label="区域" prop="regionId">
          <el-select v-model="storeForm.regionId" placeholder="请选择区域" class="w-full">
            <el-option
              v-for="region in regionList"
              :key="region.id"
              :label="region.name"
              :value="region.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="storeForm.address" placeholder="请输入店铺地址" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="storeForm.status">
            <el-radio label="active">营业中</el-radio>
            <el-radio label="inactive">已停业</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="营业时间" prop="openTime">
          <el-input v-model="storeForm.openTime" placeholder="例如：周一至周日 9:00-21:00" />
        </el-form-item>
        <el-form-item label="店铺简介">
          <el-input v-model="storeForm.description" type="textarea" :rows="3" placeholder="请输入店铺简介" />
        </el-form-item>
        <el-form-item label="店铺图片">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :file-list="imageFileList"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="formSubmitting">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除确认" width="400px">
      <p>确定要删除店铺 "{{ deleteStore.name }}" 吗？</p>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteStoreConfirm" :loading="deleteLoading">确定删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import storeManagementApi from '@/api/modules/housekeeping/store_management';

// 默认Logo
const defaultLogo = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

// 筛选条件
const selectedStatus = ref('all');
const selectedRegion = ref('all');
const keyword = ref('');

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);
const formSubmitting = ref(false);
const deleteLoading = ref(false);

// 列表数据
const regionList = ref([]);
const tableData = ref([]);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 详情对话框
const detailDialogVisible = ref(false);
const storeDetail = ref({});

// 表单对话框
const formDialogVisible = ref(false);
const isEdit = ref(false);
const storeFormRef = ref(null);
const logoUrl = ref('');
const logoFile = ref(null);
const imageFileList = ref([]);

// 删除对话框
const deleteDialogVisible = ref(false);
const deleteStore = ref({});

// 表单数据
const storeForm = reactive({
  uuid: '',
  name: '',
  phone: '',
  managerName: '',
  regionId: '',
  address: '',
  status: 'active',
  openTime: '',
  description: '',
  logo: '',
  images: []
});

// 表单验证规则
const storeRules = {
  name: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  managerName: [
    { required: true, message: '请输入店长姓名', trigger: 'blur' }
  ],
  regionId: [
    { required: true, message: '请选择区域', trigger: 'change' }
  ],
  address: [
    { required: true, message: '请输入店铺地址', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  openTime: [
    { required: true, message: '请输入营业时间', trigger: 'blur' }
  ]
};

// 获取区域列表
async function getRegionList() {
  try {
    const response = await storeManagementApi.getRegionList();

    if (response.code === 200 && response.data) {
      regionList.value = response.data.list || [];
    } else {
      regionList.value = [];
    }
  } catch (error) {
    console.error('获取区域列表失败:', error);
    regionList.value = [];
  }
}

// 获取店铺列表
async function getStoreList() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: keyword.value || undefined,
      region_id: selectedRegion.value === 'all' ? undefined : selectedRegion.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value
    };

    const response = await storeManagementApi.getStoreList(params);

    if (response.code === 200 && response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || '获取店铺列表失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error);
    ElMessage.error('获取店铺列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 获取店铺详情
async function getStoreDetail(storeUuid) {
  try {
    detailLoading.value = true;

    const response = await storeManagementApi.getStoreDetail(storeUuid);

    if (response.code === 200 && response.data) {
      storeDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || '获取店铺详情失败');
      storeDetail.value = {};
    }
  } catch (error) {
    console.error('获取店铺详情失败:', error);
    ElMessage.error('获取店铺详情失败');
    storeDetail.value = {};
  } finally {
    detailLoading.value = false;
  }
}

// 查看店铺详情
function viewDetail(row) {
  detailDialogVisible.value = true;
  getStoreDetail(row.uuid);
}

// 打开新增店铺对话框
function openCreateDialog() {
  isEdit.value = false;
  resetForm();
  formDialogVisible.value = true;
}

// 编辑店铺信息
function editStore(row) {
  isEdit.value = true;
  resetForm();

  // 获取店铺详情
  getStoreDetail(row.uuid).then(() => {
    // 填充表单数据
    storeForm.uuid = storeDetail.value.uuid;
    storeForm.name = storeDetail.value.name;
    storeForm.phone = storeDetail.value.phone;
    storeForm.managerName = storeDetail.value.managerName;
    storeForm.regionId = storeDetail.value.regionId;
    storeForm.address = storeDetail.value.address;
    storeForm.status = storeDetail.value.status;
    storeForm.openTime = storeDetail.value.openTime;
    storeForm.description = storeDetail.value.description;
    storeForm.logo = storeDetail.value.logo;

    // 设置Logo预览
    if (storeDetail.value.logo) {
      logoUrl.value = storeDetail.value.logo;
    }

    // 设置图片列表
    if (storeDetail.value.images && storeDetail.value.images.length > 0) {
      imageFileList.value = storeDetail.value.images.map(image => ({
        name: image.name,
        url: image.url,
        uid: image.uid || Date.now() + Math.random().toString(36).substring(2, 10)
      }));
      storeForm.images = storeDetail.value.images;
    }

    formDialogVisible.value = true;
  });
}

// 重置表单
function resetForm() {
  if (storeFormRef.value) {
    storeFormRef.value.resetFields();
  }

  storeForm.uuid = '';
  storeForm.name = '';
  storeForm.phone = '';
  storeForm.managerName = '';
  storeForm.regionId = '';
  storeForm.address = '';
  storeForm.status = 'active';
  storeForm.openTime = '';
  storeForm.description = '';
  storeForm.logo = '';
  storeForm.images = [];

  logoUrl.value = '';
  logoFile.value = null;
  imageFileList.value = [];
}

// 处理Logo变化
function handleLogoChange(file) {
  logoFile.value = file.raw;
  logoUrl.value = URL.createObjectURL(file.raw);
}

// 处理图片变化
function handleImageChange(file, fileList) {
  imageFileList.value = fileList;
}

// 处理图片移除
function handleImageRemove(file, fileList) {
  imageFileList.value = fileList;
}

// 上传Logo
async function uploadLogo() {
  if (!logoFile.value) {
    return storeForm.logo;
  }

  try {
    const formData = new FormData();
    formData.append('file', logoFile.value);

    const response = await storeManagementApi.uploadStoreImage(formData);

    if (response.code === 200 && response.data) {
      return response.data.url;
    } else {
      throw new Error(response.msg || '上传Logo失败');
    }
  } catch (error) {
    console.error('上传Logo失败:', error);
    ElMessage.error('上传Logo失败');
    return storeForm.logo;
  }
}

// 上传图片
async function uploadImages() {
  const newImages = imageFileList.value.filter(file => !file.url);
  const oldImages = imageFileList.value.filter(file => file.url);

  if (newImages.length === 0) {
    return oldImages;
  }

  const uploadPromises = newImages.map(async (file) => {
    const formData = new FormData();
    formData.append('file', file.raw);

    try {
      const response = await storeManagementApi.uploadStoreImage(formData);

      if (response.code === 200 && response.data) {
        return {
          name: file.name,
          url: response.data.url,
          uid: file.uid
        };
      } else {
        throw new Error(response.msg || '上传图片失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  });

  try {
    const uploadedImages = await Promise.all(uploadPromises);
    return [...oldImages, ...uploadedImages];
  } catch (error) {
    ElMessage.error('上传图片失败');
    throw error;
  }
}

// 提交表单
async function submitForm() {
  if (!storeFormRef.value) return;

  try {
    await storeFormRef.value.validate();

    formSubmitting.value = true;

    // 上传Logo和图片
    const [logoUrl, uploadedImages] = await Promise.all([
      uploadLogo(),
      uploadImages()
    ]);

    // 构建请求数据
    const data = {
      name: storeForm.name,
      phone: storeForm.phone,
      manager_name: storeForm.managerName,
      region_id: storeForm.regionId,
      address: storeForm.address,
      status: storeForm.status,
      open_time: storeForm.openTime,
      description: storeForm.description || undefined,
      logo: logoUrl || undefined,
      images: uploadedImages || []
    };

    let response;

    if (isEdit.value) {
      // 编辑店铺
      data.uuid = storeForm.uuid;
      response = await storeManagementApi.updateStore(data);
    } else {
      // 新增店铺
      response = await storeManagementApi.createStore(data);
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '编辑店铺信息成功' : '新增店铺成功');
      formDialogVisible.value = false;
      getStoreList();
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '编辑店铺信息失败' : '新增店铺失败'));
    }
  } catch (error) {
    console.error(isEdit.value ? '编辑店铺信息失败:' : '新增店铺失败:', error);
    ElMessage.error(isEdit.value ? '编辑店铺信息失败' : '新增店铺失败');
  } finally {
    formSubmitting.value = false;
  }
}

// 确认删除店铺
function confirmDelete(row) {
  deleteStore.value = row;
  deleteDialogVisible.value = true;
}

// 删除店铺
async function deleteStoreConfirm() {
  try {
    deleteLoading.value = true;

    const response = await storeManagementApi.deleteStore(deleteStore.value.uuid);

    if (response.code === 200) {
      ElMessage.success('删除店铺成功');
      deleteDialogVisible.value = false;
      getStoreList();
    } else {
      ElMessage.error(response.msg || '删除店铺失败');
    }
  } catch (error) {
    console.error('删除店铺失败:', error);
    ElMessage.error('删除店铺失败');
  } finally {
    deleteLoading.value = false;
  }
}

// 导出店铺数据
async function exportData() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      keyword: keyword.value || undefined,
      region_id: selectedRegion.value === 'all' ? undefined : selectedRegion.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value
    };

    const response = await storeManagementApi.exportStoreData(params);

    // 创建一个下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `店铺数据_${new Date().getTime()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出店铺数据失败:', error);
    ElMessage.error('导出店铺数据失败');
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function handleReset() {
  selectedStatus.value = 'all';
  selectedRegion.value = 'all';
  keyword.value = '';

  handleSearch();
}

// 查询数据
function handleSearch() {
  currentPage.value = 1;
  getStoreList();
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
  getStoreList();
}

function handleCurrentChange(val) {
  currentPage.value = val;
  getStoreList();
}

// 页面加载时获取数据
onMounted(() => {
  getRegionList().then(() => {
    getStoreList();
  });
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
