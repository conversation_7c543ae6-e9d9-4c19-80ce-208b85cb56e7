# 应用配置面板
VITE_APP_SETTING = true
# 页面标题
VITE_APP_TITLE = 金刚到家管理系统
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://192.168.0.25:9099
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL = eruda
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false

# 是否开启代理
VITE_OPEN_PROXY = false

# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = false

# 高德地图 API Key
VITE_AMAP_KEY=efa5663786421925675534660d44b972

# 高德地图安全密钥（可选，提高安全性）
VITE_AMAP_SECURITY_CODE=c5a7f48036240dcf809926ce3bfb20c9
