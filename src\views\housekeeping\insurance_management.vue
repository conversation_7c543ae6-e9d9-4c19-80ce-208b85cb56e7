<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">保险管理</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">保险公司</span>
            <el-select v-model="selectedCompany" placeholder="选择公司" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="company in companyList"
                :key="company.id"
                :label="company.name"
                :value="company.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">保险类型</span>
            <el-select v-model="selectedType" placeholder="选择类型" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="type in typeList"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">关键词</span>
            <el-input v-model="keyword" placeholder="保险名称/保险编号" class="w-[250px]" />
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap" @click="openCreateDialog">
              新增保险产品
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" class="w-full">
        <el-table-column prop="productCode" label="产品编号" width="120" />
        <el-table-column prop="productName" label="产品名称" width="180" />
        <el-table-column prop="insuranceCompany" label="保险公司" width="120" />
        <el-table-column prop="insuranceType" label="保险类型" width="120" />
        <el-table-column prop="premium" label="保费" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.premium }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="insuredAmount" label="保额" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.insuredAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="insurancePeriod" label="保险期限" width="100">
          <template #default="scope">
            <span>{{ scope.row.insurancePeriod }}天</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看</el-button>
            <el-button type="warning" link @click="editProduct(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="confirmDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 保险产品详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="保险产品详情" width="800px">
      <div v-if="detailLoading" class="flex justify-center items-center py-20">
        <el-spinner size="large" />
      </div>
      <div v-else>
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">产品编号</span>
            <span class="font-medium">{{ productDetail.productCode }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">产品名称</span>
            <span class="font-medium">{{ productDetail.productName }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保险公司</span>
            <span class="font-medium">{{ productDetail.insuranceCompany }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保险类型</span>
            <span class="font-medium">{{ productDetail.insuranceType }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保费</span>
            <span class="font-medium">¥{{ productDetail.premium }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保额</span>
            <span class="font-medium">¥{{ productDetail.insuredAmount }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保险期限</span>
            <span class="font-medium">{{ productDetail.insurancePeriod }}天</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">创建时间</span>
            <span class="font-medium">{{ productDetail.createTime }}</span>
          </div>
        </div>

        <div class="mb-6">
          <span class="text-gray-500 text-sm">产品描述</span>
          <div class="mt-1 p-3 border rounded-lg">
            {{ productDetail.description || '无' }}
          </div>
        </div>

        <div class="mb-6">
          <span class="text-gray-500 text-sm">保障范围</span>
          <div class="mt-1 p-3 border rounded-lg">
            {{ productDetail.coverage || '无' }}
          </div>
        </div>

        <div class="mb-6">
          <span class="text-gray-500 text-sm">免责条款</span>
          <div class="mt-1 p-3 border rounded-lg">
            {{ productDetail.exclusions || '无' }}
          </div>
        </div>

        <div v-if="productDetail.images && productDetail.images.length > 0" class="mb-6">
          <span class="text-gray-500 text-sm">产品图片</span>
          <div class="mt-1 grid grid-cols-4 gap-4">
            <div v-for="(image, index) in productDetail.images" :key="index" class="border rounded-lg p-2">
              <img :src="image.url" class="w-full h-24 object-cover" />
            </div>
          </div>
        </div>

        <div v-if="productDetail.attachments && productDetail.attachments.length > 0">
          <span class="text-gray-500 text-sm">附件</span>
          <div class="mt-1 grid grid-cols-2 gap-4">
            <div v-for="(attachment, index) in productDetail.attachments" :key="index" class="border rounded-lg p-2">
              <div class="flex items-center justify-between">
                <span class="truncate">{{ attachment.name }}</span>
                <el-button type="primary" link @click="downloadAttachment(attachment)">下载</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑保险产品对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑保险产品' : '新增保险产品'"
      width="800px"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
      >
        <el-form-item label="产品编号" prop="productCode">
          <el-input v-model="productForm.productCode" placeholder="请输入产品编号" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="productForm.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="保险公司" prop="companyId">
          <el-select v-model="productForm.companyId" placeholder="请选择保险公司" class="w-full">
            <el-option
              v-for="company in companyList"
              :key="company.id"
              :label="company.name"
              :value="company.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="保险类型" prop="typeId">
          <el-select v-model="productForm.typeId" placeholder="请选择保险类型" class="w-full">
            <el-option
              v-for="type in typeList"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="保费" prop="premium">
          <el-input-number v-model="productForm.premium" :precision="2" :min="0" class="w-full" />
        </el-form-item>
        <el-form-item label="保额" prop="insuredAmount">
          <el-input-number v-model="productForm.insuredAmount" :precision="2" :min="0" class="w-full" />
        </el-form-item>
        <el-form-item label="保险期限" prop="insurancePeriod">
          <el-input-number v-model="productForm.insurancePeriod" :min="1" :precision="0" class="w-full">
            <template #suffix>天</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="产品描述" prop="description">
          <el-input v-model="productForm.description" type="textarea" :rows="3" placeholder="请输入产品描述" />
        </el-form-item>
        <el-form-item label="保障范围" prop="coverage">
          <el-input v-model="productForm.coverage" type="textarea" :rows="3" placeholder="请输入保障范围" />
        </el-form-item>
        <el-form-item label="免责条款" prop="exclusions">
          <el-input v-model="productForm.exclusions" type="textarea" :rows="3" placeholder="请输入免责条款" />
        </el-form-item>
        <el-form-item label="产品图片">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :file-list="imageFileList"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            action="#"
            :auto-upload="false"
            :file-list="attachmentFileList"
            :on-change="handleAttachmentChange"
            :on-remove="handleAttachmentRemove"
          >
            <el-button type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="formSubmitting">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除确认" width="400px">
      <p>确定要删除保险产品 "{{ deleteProduct.productName }}" 吗？</p>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteProductConfirm" :loading="deleteLoading">确定删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import insuranceManagementApi from '@/api/modules/housekeeping/insurance_management';

// 筛选条件
const selectedCompany = ref('all');
const selectedType = ref('all');
const keyword = ref('');

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);
const formSubmitting = ref(false);
const deleteLoading = ref(false);

// 列表数据
const companyList = ref([]);
const typeList = ref([]);
const tableData = ref([]);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 详情对话框
const detailDialogVisible = ref(false);
const productDetail = ref({});

// 表单对话框
const formDialogVisible = ref(false);
const isEdit = ref(false);
const productFormRef = ref(null);
const imageFileList = ref([]);
const attachmentFileList = ref([]);

// 删除对话框
const deleteDialogVisible = ref(false);
const deleteProduct = ref({});

// 表单数据
const productForm = reactive({
  id: '',
  productCode: '',
  productName: '',
  companyId: '',
  typeId: '',
  premium: 0,
  insuredAmount: 0,
  insurancePeriod: 365,
  description: '',
  coverage: '',
  exclusions: '',
  images: [],
  attachments: []
});

// 表单验证规则
const productRules = {
  productCode: [
    { required: true, message: '请输入产品编号', trigger: 'blur' }
  ],
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  companyId: [
    { required: true, message: '请选择保险公司', trigger: 'change' }
  ],
  typeId: [
    { required: true, message: '请选择保险类型', trigger: 'change' }
  ],
  premium: [
    { required: true, message: '请输入保费', trigger: 'blur' }
  ],
  insuredAmount: [
    { required: true, message: '请输入保额', trigger: 'blur' }
  ],
  insurancePeriod: [
    { required: true, message: '请输入保险期限', trigger: 'blur' }
  ]
};

// 获取保险公司列表
async function getInsuranceCompanyList() {
  try {
    const response = await insuranceManagementApi.getInsuranceCompanyList();

    if (response.code === 200 && response.data) {
      companyList.value = response.data.list || [];
    } else {
      companyList.value = [];
    }
  } catch (error) {
    console.error('获取保险公司列表失败:', error);
    companyList.value = [];
  }
}

// 获取保险类型列表
async function getInsuranceTypeList() {
  try {
    const response = await insuranceManagementApi.getInsuranceTypeList();

    if (response.code === 200 && response.data) {
      typeList.value = response.data.list || [];
    } else {
      typeList.value = [];
    }
  } catch (error) {
    console.error('获取保险类型列表失败:', error);
    typeList.value = [];
  }
}

// 获取保险产品列表
async function getInsuranceProductList() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: keyword.value || undefined,
      company_id: selectedCompany.value === 'all' ? undefined : selectedCompany.value,
      type_id: selectedType.value === 'all' ? undefined : selectedType.value
    };

    const response = await insuranceManagementApi.getInsuranceProductList(params);

    if (response.code === 200 && response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || '获取保险产品列表失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取保险产品列表失败:', error);
    ElMessage.error('获取保险产品列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 获取保险产品详情
async function getInsuranceProductDetail(productId) {
  try {
    detailLoading.value = true;

    const response = await insuranceManagementApi.getInsuranceProductDetail(productId);

    if (response.code === 200 && response.data) {
      productDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || '获取保险产品详情失败');
      productDetail.value = {};
    }
  } catch (error) {
    console.error('获取保险产品详情失败:', error);
    ElMessage.error('获取保险产品详情失败');
    productDetail.value = {};
  } finally {
    detailLoading.value = false;
  }
}

// 查看保险产品详情
function viewDetail(row) {
  detailDialogVisible.value = true;
  getInsuranceProductDetail(row.id);
}

// 打开新增保险产品对话框
function openCreateDialog() {
  isEdit.value = false;
  resetForm();
  formDialogVisible.value = true;
}

// 编辑保险产品
function editProduct(row) {
  isEdit.value = true;
  resetForm();

  // 获取保险产品详情
  getInsuranceProductDetail(row.id).then(() => {
    // 填充表单数据
    productForm.id = productDetail.value.id;
    productForm.productCode = productDetail.value.productCode;
    productForm.productName = productDetail.value.productName;
    productForm.companyId = productDetail.value.companyId;
    productForm.typeId = productDetail.value.typeId;
    productForm.premium = productDetail.value.premium;
    productForm.insuredAmount = productDetail.value.insuredAmount;
    productForm.insurancePeriod = productDetail.value.insurancePeriod;
    productForm.description = productDetail.value.description;
    productForm.coverage = productDetail.value.coverage;
    productForm.exclusions = productDetail.value.exclusions;

    // 填充图片和附件数据
    if (productDetail.value.images && productDetail.value.images.length > 0) {
      imageFileList.value = productDetail.value.images.map(image => ({
        name: image.name,
        url: image.url,
        uid: image.uid || Date.now() + Math.random().toString(36).substring(2, 10)
      }));
      productForm.images = productDetail.value.images;
    }

    if (productDetail.value.attachments && productDetail.value.attachments.length > 0) {
      attachmentFileList.value = productDetail.value.attachments.map(attachment => ({
        name: attachment.name,
        url: attachment.url,
        uid: attachment.uid || Date.now() + Math.random().toString(36).substring(2, 10)
      }));
      productForm.attachments = productDetail.value.attachments;
    }

    formDialogVisible.value = true;
  });
}

// 重置表单
function resetForm() {
  if (productFormRef.value) {
    productFormRef.value.resetFields();
  }

  productForm.id = '';
  productForm.productCode = '';
  productForm.productName = '';
  productForm.companyId = '';
  productForm.typeId = '';
  productForm.premium = 0;
  productForm.insuredAmount = 0;
  productForm.insurancePeriod = 365;
  productForm.description = '';
  productForm.coverage = '';
  productForm.exclusions = '';
  productForm.images = [];
  productForm.attachments = [];

  imageFileList.value = [];
  attachmentFileList.value = [];
}

// 处理图片变化
function handleImageChange(file, fileList) {
  imageFileList.value = fileList;
}

// 处理图片移除
function handleImageRemove(file, fileList) {
  imageFileList.value = fileList;
}

// 处理附件变化
function handleAttachmentChange(file, fileList) {
  attachmentFileList.value = fileList;
}

// 处理附件移除
function handleAttachmentRemove(file, fileList) {
  attachmentFileList.value = fileList;
}

// 上传图片
async function uploadImages() {
  const newImages = imageFileList.value.filter(file => !file.url);
  const oldImages = imageFileList.value.filter(file => file.url);

  if (newImages.length === 0) {
    return oldImages;
  }

  const uploadPromises = newImages.map(async (file) => {
    const formData = new FormData();
    formData.append('file', file.raw);

    try {
      const response = await insuranceManagementApi.uploadProductImage(formData);

      if (response.code === 200 && response.data) {
        return {
          name: file.name,
          url: response.data.url,
          uid: file.uid
        };
      } else {
        throw new Error(response.msg || '上传图片失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  });

  try {
    const uploadedImages = await Promise.all(uploadPromises);
    return [...oldImages, ...uploadedImages];
  } catch (error) {
    ElMessage.error('上传图片失败');
    throw error;
  }
}

// 上传附件
async function uploadAttachments() {
  const newAttachments = attachmentFileList.value.filter(file => !file.url);
  const oldAttachments = attachmentFileList.value.filter(file => file.url);

  if (newAttachments.length === 0) {
    return oldAttachments;
  }

  const uploadPromises = newAttachments.map(async (file) => {
    const formData = new FormData();
    formData.append('file', file.raw);

    try {
      const response = await insuranceManagementApi.uploadProductAttachment(formData);

      if (response.code === 200 && response.data) {
        return {
          name: file.name,
          url: response.data.url,
          uid: file.uid
        };
      } else {
        throw new Error(response.msg || '上传附件失败');
      }
    } catch (error) {
      console.error('上传附件失败:', error);
      throw error;
    }
  });

  try {
    const uploadedAttachments = await Promise.all(uploadPromises);
    return [...oldAttachments, ...uploadedAttachments];
  } catch (error) {
    ElMessage.error('上传附件失败');
    throw error;
  }
}

// 提交表单
async function submitForm() {
  if (!productFormRef.value) return;

  try {
    await productFormRef.value.validate();

    formSubmitting.value = true;

    // 上传图片和附件
    const [uploadedImages, uploadedAttachments] = await Promise.all([
      uploadImages(),
      uploadAttachments()
    ]);

    // 构建请求数据
    const data = {
      product_code: productForm.productCode,
      product_name: productForm.productName,
      company_id: productForm.companyId,
      type_id: productForm.typeId,
      premium: productForm.premium,
      insured_amount: productForm.insuredAmount,
      insurance_period: productForm.insurancePeriod,
      description: productForm.description,
      coverage: productForm.coverage,
      exclusions: productForm.exclusions,
      images: uploadedImages,
      attachments: uploadedAttachments
    };

    let response;

    if (isEdit.value) {
      // 编辑保险产品
      data.id = productForm.id;
      response = await insuranceManagementApi.updateInsuranceProduct(data);
    } else {
      // 新增保险产品
      response = await insuranceManagementApi.createInsuranceProduct(data);
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '编辑保险产品成功' : '新增保险产品成功');
      formDialogVisible.value = false;
      getInsuranceProductList();
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '编辑保险产品失败' : '新增保险产品失败'));
    }
  } catch (error) {
    console.error(isEdit.value ? '编辑保险产品失败:' : '新增保险产品失败:', error);
    ElMessage.error(isEdit.value ? '编辑保险产品失败' : '新增保险产品失败');
  } finally {
    formSubmitting.value = false;
  }
}

// 确认删除保险产品
function confirmDelete(row) {
  deleteProduct.value = row;
  deleteDialogVisible.value = true;
}

// 删除保险产品
async function deleteProductConfirm() {
  try {
    deleteLoading.value = true;

    const response = await insuranceManagementApi.deleteInsuranceProduct(deleteProduct.value.id);

    if (response.code === 200) {
      ElMessage.success('删除保险产品成功');
      deleteDialogVisible.value = false;
      getInsuranceProductList();
    } else {
      ElMessage.error(response.msg || '删除保险产品失败');
    }
  } catch (error) {
    console.error('删除保险产品失败:', error);
    ElMessage.error('删除保险产品失败');
  } finally {
    deleteLoading.value = false;
  }
}

// 下载附件
function downloadAttachment(attachment) {
  if (attachment && attachment.url) {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || `attachment_${new Date().getTime()}`;
    link.target = '_blank';
    link.click();
  }
}

// 重置筛选条件
function handleReset() {
  selectedCompany.value = 'all';
  selectedType.value = 'all';
  keyword.value = '';

  handleSearch();
}

// 查询数据
function handleSearch() {
  currentPage.value = 1;
  getInsuranceProductList();
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
  getInsuranceProductList();
}

function handleCurrentChange(val) {
  currentPage.value = val;
  getInsuranceProductList();
}

// 页面加载时获取数据
onMounted(() => {
  Promise.all([
    getInsuranceCompanyList(),
    getInsuranceTypeList()
  ]).then(() => {
    getInsuranceProductList();
  });
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
