<template>
  <div class="mx-auto p-4 container">
    <div class="mb-6">
      <h1 class="mb-4 text-2xl font-bold">
        保险购买
      </h1>

      <!-- 保险产品列表 -->
      <div class="mb-6 rounded-lg bg-white p-4 shadow">
        <div class="mb-4 flex items-center justify-between">
          <h2 class="text-xl font-semibold">
            保险产品列表
          </h2>
          <el-button type="primary" @click="refreshProductList">
            刷新列表
          </el-button>
        </div>

        <el-table v-loading="loading" :data="productList" border style="width: 100%;">
          <el-table-column prop="name" label="保险名称" width="180" />
          <el-table-column prop="insurance_company" label="保险公司" width="180" />
          <el-table-column prop="explain_content" label="保险说明" show-overflow-tooltip />
          <el-table-column label="保费" width="180">
            <template #default="scope">
              <div v-for="(item, index) in scope.row.premium" :key="index" class="mb-1">
                {{ item.unit }}: {{ item.money }} 元
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleBuyInsurance(scope.row)">
                购买
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 保险订单列表 -->
      <div class="rounded-lg bg-white p-4 shadow">
        <div class="mb-4 flex items-center justify-between">
          <h2 class="text-xl font-semibold">
            我的保险订单
          </h2>
          <el-button type="primary" @click="getInsuranceOrders">
            刷新订单
          </el-button>
        </div>

        <el-table v-loading="orderLoading" :data="orderList" border style="width: 100%;">
          <el-table-column prop="order_number" label="订单编号" width="180" />
          <el-table-column prop="insurance_name" label="保险名称" width="180" />
          <el-table-column prop="insurance_company" label="保险公司" width="180" />
          <el-table-column prop="pay_amount" label="支付金额" width="120">
            <template #default="scope">
              {{ scope.row.pay_amount }} 元
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="180" />
          <el-table-column prop="status_name" label="状态" width="120" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewInsuranceOrder(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 购买保险对话框 -->
    <el-dialog v-model="buyDialogVisible" title="购买保险" width="700px">
      <div v-if="selectedProduct" class="p-4">
        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            保险信息
          </h3>
          <div class="grid grid-cols-2 mb-4 gap-4">
            <div>
              <span class="text-gray-600">保险名称：</span>
              <span>{{ selectedProduct.name }}</span>
            </div>
            <div>
              <span class="text-gray-600">保险公司：</span>
              <span>{{ selectedProduct.insurance_company }}</span>
            </div>
          </div>
          <div class="mb-4">
            <span class="text-gray-600">保险说明：</span>
            <span>{{ selectedProduct.explain_content }}</span>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            选择保险方案
          </h3>
          <el-radio-group v-model="selectedPremium">
            <el-radio
              v-for="(item, index) in selectedProduct.premium"
              :key="index"
              :label="index"
              class="mb-2 block"
            >
              {{ item.unit }} - {{ item.money }} 元 (有效期: {{ item.start_time }} 至 {{ item.end_time }})
            </el-radio>
          </el-radio-group>
        </div>

        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            被保险人信息
          </h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="mb-4">
              <div class="mb-1">
                姓名
              </div>
              <el-input v-model="insuranceForm.name" placeholder="请输入被保险人姓名" />
            </div>
            <div class="mb-4">
              <div class="mb-1">
                身份证号
              </div>
              <el-input v-model="insuranceForm.id_card" placeholder="请输入被保险人身份证号" />
            </div>
            <div class="mb-4">
              <div class="mb-1">
                手机号
              </div>
              <el-input v-model="insuranceForm.phone" placeholder="请输入被保险人手机号" />
            </div>
            <div class="mb-4">
              <div class="mb-1">
                服务类型
              </div>
              <el-select v-model="insuranceForm.service_type" placeholder="请选择服务类型" class="w-full">
                <el-option label="家政服务" value="housekeeping" />
                <el-option label="月嫂" value="matron" />
                <el-option label="保姆" value="nanny" />
                <el-option label="老人院" value="eldercare" />
              </el-select>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="buyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitInsuranceOrder">提交订单</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看保险订单对话框 -->
    <el-dialog v-model="viewDialogVisible" title="保险订单详情" width="700px">
      <div v-if="selectedOrder" class="p-4">
        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            订单信息
          </h3>
          <div class="grid grid-cols-2 mb-4 gap-4">
            <div>
              <span class="text-gray-600">订单编号：</span>
              <span>{{ selectedOrder.order_number }}</span>
            </div>
            <div>
              <span class="text-gray-600">保险名称：</span>
              <span>{{ selectedOrder.insurance_name }}</span>
            </div>
            <div>
              <span class="text-gray-600">保险公司：</span>
              <span>{{ selectedOrder.insurance_company }}</span>
            </div>
            <div>
              <span class="text-gray-600">支付金额：</span>
              <span>{{ selectedOrder.pay_amount }} 元</span>
            </div>
            <div>
              <span class="text-gray-600">创建时间：</span>
              <span>{{ selectedOrder.create_time }}</span>
            </div>
            <div>
              <span class="text-gray-600">状态：</span>
              <span>{{ selectedOrder.status_name }}</span>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            被保险人信息
          </h3>
          <div class="grid grid-cols-2 mb-4 gap-4">
            <div v-for="(person, index) in selectedOrder.recognizees" :key="index">
              <div class="mb-2 border rounded p-3">
                <div class="mb-1">
                  <span class="text-gray-600">姓名：</span>
                  <span>{{ person.name }}</span>
                </div>
                <div class="mb-1">
                  <span class="text-gray-600">身份证号：</span>
                  <span>{{ person.id_card }}</span>
                </div>
                <div>
                  <span class="text-gray-600">手机号：</span>
                  <span>{{ person.phone }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedOrder.policy_url" class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">
            保单信息
          </h3>
          <el-button type="primary" @click="downloadPolicy(selectedOrder.policy_url)">
            下载电子保单
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import buyInsurance from '@/api/modules/housekeeping/buy_insurance'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

// 保险产品列表
const productList = ref([])
const loading = ref(false)

// 保险订单列表
const orderList = ref([])
const orderLoading = ref(false)

// 对话框控制
const buyDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitLoading = ref(false)

// 选中的保险产品和订单
const selectedProduct = ref(null)
const selectedOrder = ref(null)
const selectedPremium = ref(0)

// 保险表单
const insuranceForm = reactive({
  name: '',
  id_card: '',
  phone: '',
  service_type: 'housekeeping',
})

// 获取保险产品列表
async function getProductList() {
  try {
    loading.value = true
    const response = await buyInsurance.findProductList()

    if (response.code === 200 && response.data) {
      productList.value = response.data.list || []
    }
    else {
      ElMessage.error(response.msg || '获取保险产品列表失败')
      productList.value = []
    }
  }
  catch (error) {
    console.error('获取保险产品列表失败:', error)
    ElMessage.error('获取保险产品列表失败')
    productList.value = []
  }
  finally {
    loading.value = false
  }
}

// 刷新保险产品列表
function refreshProductList() {
  getProductList()
}

// 获取保险订单列表
async function getInsuranceOrders() {
  try {
    orderLoading.value = true
    const params = {
      page: 1,
      size: 20,
    }

    const response = await buyInsurance.findInsuranceOrder(params)

    if (response.code === 200 && response.data) {
      orderList.value = response.data.list || []
    }
    else {
      ElMessage.error(response.msg || '获取保险订单列表失败')
      orderList.value = []
    }
  }
  catch (error) {
    console.error('获取保险订单列表失败:', error)
    ElMessage.error('获取保险订单列表失败')
    orderList.value = []
  }
  finally {
    orderLoading.value = false
  }
}

// 处理购买保险
function handleBuyInsurance(product) {
  selectedProduct.value = product
  selectedPremium.value = 0 // 默认选择第一个保险方案

  // 重置表单
  insuranceForm.name = ''
  insuranceForm.id_card = ''
  insuranceForm.phone = ''
  insuranceForm.service_type = 'housekeeping'

  // 显示对话框
  buyDialogVisible.value = true
}

// 提交保险订单
async function submitInsuranceOrder() {
  try {
    // 验证表单
    if (!insuranceForm.name) {
      ElMessage.warning('请输入被保险人姓名')
      return
    }

    if (!insuranceForm.id_card) {
      ElMessage.warning('请输入被保险人身份证号')
      return
    }

    if (!insuranceForm.phone) {
      ElMessage.warning('请输入被保险人手机号')
      return
    }

    if (!selectedProduct.value || selectedPremium.value === undefined) {
      ElMessage.warning('请选择保险方案')
      return
    }

    // 获取选中的保险方案
    const premium = selectedProduct.value.premium[selectedPremium.value]

    // 构建请求参数
    const orderData = {
      plan_id: selectedProduct.value.uuid,
      insurance_name: selectedProduct.value.name,
      service_type: insuranceForm.service_type,
      duration_unit: premium.name, // month 或 year
      store_name: '北京朝阳店', // 实际应使用当前店铺名称
      company_name: '家政公司', // 实际应使用当前公司名称
      recognizees: [
        {
          name: insuranceForm.name,
          id_card: insuranceForm.id_card,
          phone: insuranceForm.phone,
        },
      ],
      pay_amount: premium.money,
    }

    // 设置提交状态
    submitLoading.value = true

    // 调用API创建保险订单
    const response = await buyInsurance.createInsuranceOrder(orderData)

    if (response.code === 200) {
      ElMessage.success('保险订单创建成功')
      buyDialogVisible.value = false

      // 重新获取保险订单列表
      getInsuranceOrders()
    }
    else {
      ElMessage.error(response.msg || '创建保险订单失败')
    }
  }
  catch (error) {
    console.error('创建保险订单失败:', error)
    ElMessage.error('创建保险订单失败')
  }
  finally {
    submitLoading.value = false
  }
}

// 查看保险订单
function viewInsuranceOrder(order) {
  selectedOrder.value = order
  viewDialogVisible.value = true
}

// 下载电子保单
function downloadPolicy(url) {
  if (!url) {
    ElMessage.warning('电子保单暂不可用')
    return
  }

  // 在新标签页中打开链接
  window.open(url, '_blank')
}

// 页面加载时获取保险产品列表和订单列表
onMounted(() => {
  getProductList()
  getInsuranceOrders()
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
