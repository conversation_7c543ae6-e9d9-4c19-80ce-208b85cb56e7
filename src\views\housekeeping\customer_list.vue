<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-[1440px] px-6 py-4">
      <!-- 顶部按钮组 -->
      <div class="mb-6 flex gap-3">
        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="dialogVisible = true">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>新建线索
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap" @click="importDialogVisible = true">
          <el-icon class="mr-1">
            <Upload />
          </el-icon>批量导入客户线索
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap" @click="$router.push('/housekeeping/import_records')">
          <el-icon class="mr-1">
            <Document />
          </el-icon>导入记录
        </el-button>
      </div>
      <!-- 搜索区域 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 flex flex-wrap gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">搜索客户</span>
            <el-select v-model="searchType" class="!rounded-button w-24">
              <el-option label="全部" value="all" />
              <el-option label="姓名" value="name" />
              <el-option label="电话" value="phone" />
            </el-select>
            <el-input v-model="seachForm.keyword" placeholder="请输入姓名、电话关键字" class="w-64" />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">所属门店</span>
            <el-select v-model="store" class="!rounded-button w-48">
              <el-option label="厦门小羽佳线索店" value="store1" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">销售归属</span>
            <el-select v-model="salesOwner" class="!rounded-button w-32">
              <el-option label="不限" value="unlimited" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">售后归属</span>
            <el-select v-model="serviceOwner" class="!rounded-button w-32">
              <el-option label="不限" value="unlimited" />
            </el-select>
          </div>
        </div>
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">下次跟进时间</span>
            <div class="flex gap-2">
              <div class="flex flex-wrap gap-2">
                <el-button
                  v-for="(btn, index) in timeBtns"
                  :key="index"
                  :type="selectedTimeBtn === btn.value ? 'primary' : ''"
                  size="small"
                  class="!rounded-button"
                  @click="handleFollowTimeClick(index, btn)"
                >
                  {{ btn.label }}
                </el-button>
              </div>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="起始日期"
                end-placeholder="结束日期"
                class="w-80"
              />
            </div>
          </div>
          <div v-show="showMoreFilters" class="w-full">
            <!-- 录入时间 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">录入时间:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(btn, index) in timeBtns"
                    :key="index"
                    :type="selectedCreateTimeBtn === btn.value ? 'primary' : ''"
                    size="small"
                    class="!rounded-button"
                    @click="handleCreateTimeClick(index, btn)"
                  >
                    {{ btn.label }}
                  </el-button>
                </div>
                <el-date-picker
                  v-model="createTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始日期"
                  end-placeholder="结束日期"
                  class="w-80"
                />
              </div>
            </div>
            <!-- 更新时间 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">更新时间:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(btn, index) in timeBtns"
                    :key="index"
                    :type="selectedUpdateTimeBtn === btn.value ? 'primary' : ''"
                    size="small"
                    class="!rounded-button"
                    @click="handleUpdateTimeClick(index, btn)"
                  >
                    {{ btn.label }}
                  </el-button>
                </div>
                <el-date-picker
                  v-model="updateTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始日期"
                  end-placeholder="结束日期"
                  class="w-80"
                />
              </div>
            </div>
            <!-- 类型 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">类型:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(item, index) in searchFormList.demand"
                    :key="index"
                    :type="selectedTypeIndex === index ? 'primary' : ''"
                    size="small"
                    class="!rounded-button"
                    @click="selectedTypeIndex = index; selectedType = item.value || item"
                  >
                    {{ typeof item === 'string' ? item : item.name }}
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 标签 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">标签:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(item, index) in searchFormList.tag"
                    :key="index"
                    :type="selectedTagIndex === index ? 'primary' : ''"
                    size="small"
                    class="!rounded-button"
                    @click="selectedTagIndex = index; selectedTag = item.value"
                  >
                    {{ item.name }}
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 来源 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">来源:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(item, index) in searchFormList.source"
                    :key="index"
                    :type="selectedSourceIndex === index ? 'primary' : ''"
                    size="small"
                    class="!rounded-button"
                    @click="selectedSourceIndex = index; selectedSource = item.value"
                  >
                    {{ item.name }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ml-auto mt-4 flex items-center gap-3">
          <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="getCustomerList()">
            <el-icon class="mr-1">
              <Search />
            </el-icon>查询
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap" @click="resetSearchForm">
            <el-icon class="mr-1">
              <Refresh />
            </el-icon>重置
          </el-button>
          <div class="flex items-center gap-4">
            <el-button class="!rounded-button whitespace-nowrap" @click="showMoreFilters = !showMoreFilters">
              {{ showMoreFilters ? '收起' : '更多筛选条件' }}
              <el-icon class="ml-1">
                <component :is="showMoreFilters ? ArrowUp : ArrowDown" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
      <!-- 标签页 -->
      <div class="mb-4">
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="`全部(${tabCounts.all || 0})`" name="all" />
          <el-tab-pane :label="`待跟进线索(${tabCounts.pending || 0})`" name="pending" />
          <el-tab-pane :label="`跟进中潜客(${tabCounts.following || 0})`" name="following" />
          <el-tab-pane :label="`已签约客户(${tabCounts.signed || 0})`" name="signed" />
          <el-tab-pane :label="`失效公共池(${tabCounts.invalid || 0})`" name="invalid" />
        </el-tabs>
      </div>
      <!-- 批量操作按钮 -->
      <div class="mb-4 flex gap-3">
        <el-button class="!rounded-button whitespace-nowrap">
          批量变更销售归属人
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap">
          批量变更售后归属人
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap">
          批量变更跟进状态
        </el-button>
      </div>
      <!-- 表格 -->
      <el-table :data="tableData" style="width: 100%;" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="客户信息" min-width="150">
          <template #default="{ row }">
            <div class="flex items-center">
              <span>{{ row.name }}</span>
              <el-icon class="ml-1 cursor-pointer text-gray-400">
                <CopyDocument />
              </el-icon>
            </div>
            <div class="mt-1 text-gray-400">
              {{ row.mobile }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="需求" prop="aunt_type" min-width="150">
          <template #default="{ row }">
            {{ row.aunt_type || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="150">
          <template #default="{ row }">
            <div class="mb-2 flex flex-wrap gap-1">
              <!-- 标签列表 -->
              <el-tag
                v-for="tag in row.tags" v-if="row.tags && row.tags.length" :key="tag" size="small"
                class="!rounded-full" type="success"
              >
                {{ tag }}
              </el-tag>
              <span v-if="!row.tags || !row.tags.length">暂无标签</span>
            </div>
            <el-button size="small" class="!rounded-button whitespace-nowrap" @click="handleEditTags(row)">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>编辑
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" min-width="150">
          <template #default="{ row }">
            <el-tag v-if="row.status === '1'" type="warning">
              {{ row.status_name || '待跟进' }}
            </el-tag>
            <el-tag v-else-if="row.status === '2'" type="primary">
              {{ row.status_name || '跟进中' }}
            </el-tag>
            <el-tag v-else-if="row.status === '3'" type="success">
              {{ row.status_name || '已签约' }}
            </el-tag>
            <el-tag v-else-if="row.status === '0'" type="info">
              {{ row.status_name || '无效' }}
            </el-tag>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="最新跟进" min-width="150">
          <template #default="{ row }">
            <div class="text-red-500">
              {{ row.follow_time || '暂无跟进记录' }}
            </div>
            <el-button size="small" type="primary" class="!rounded-button mt-2 whitespace-nowrap" @click="viewFollowRecords(row)">
              查看跟进
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="销售" prop="user_name" min-width="150">
          <template #default="{ row }">
            {{ row.user_name || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="客户来源" prop="source" min-width="150">
          <template #default="{ row }">
            {{ row.source || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="update_time" min-width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.update_time) || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="入户时间" prop="create_time" min-width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="薪资范围" min-width="150">
          <template #default="{ row }">
            <span v-if="row.min_salary || row.max_salary">
              {{ row.min_salary || '--' }} - {{ row.max_salary || '--' }} {{ row.salary_unit || '元' }}
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right" min-width="150">
          <template #default="{ row }">
            <el-button type="primary" link @click="openDrawer(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>

  <!-- 客户详情抽屉 -->
  <el-drawer v-model="drawerVisible" title="客户详情" size="920px" :with-header="false">
    <div class="p-6">
      <!-- 顶部操作栏 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="text-xl font-medium">
          客户详情
        </div>
        <div class="flex items-center gap-4">
          <el-button class="!rounded-button">
            编辑客户信息
          </el-button>
          <el-button type="primary" class="!rounded-button" @click="showAddContractDialog = true">
            添加合同
          </el-button>
          <el-button type="danger" class="!rounded-button" @click="confirmDeleteCustomer">
            删除客户
          </el-button>
          <el-icon class="cursor-pointer text-xl text-gray-400" @click="drawerVisible = false">
            <Close />
          </el-icon>
        </div>
      </div>
      <!-- 客户基本信息 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-4">
        <div v-if="customer" class="mb-4 flex items-center gap-4">
          <div class="h-12 w-12 flex items-center justify-center rounded-full bg-blue-500 text-xl text-white">
            {{ customer.name ? customer.name.substring(0, 1) : '客' }}
          </div>
          <div>
            <div class="text-lg font-medium">
              {{ customer.name || '--' }}
            </div>
            <div class="text-gray-500">
              {{ customer.source || '--' }} | {{ customer.aunt_type || '--' }}
            </div>
            <div class="text-gray-500">
              电话：{{ customer.mobile || '--' }}
            </div>
            <div class="text-gray-500">
              编号：{{ customer.number || '--' }}
            </div>
          </div>
        </div>
        <div class="flex gap-4">
          <el-tag
            v-for="(tag, index) in customer?.tags || []"
            :key="index"
            class="!rounded-full"
            type="success"
          >
            {{ tag }}
          </el-tag>
          <el-button size="small" class="!rounded-button" @click="showAddTagDialog = true">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>添加标签
          </el-button>
        </div>
      </div>
      <!-- 客户状态信息 -->
      <div class="grid grid-cols-3 mb-6 gap-4">
        <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
          <span class="text-gray-500">客户状态：</span>
          <span>
            <el-tag v-if="customer?.status === '1'" type="warning">
              {{ customer?.status_name || '待跟进' }}
            </el-tag>
            <el-tag v-else-if="customer?.status === '2'" type="primary">
              {{ customer?.status_name || '跟进中' }}
            </el-tag>
            <el-tag v-else-if="customer?.status === '3'" type="success">
              {{ customer?.status_name || '已签约' }}
            </el-tag>
            <el-tag v-else-if="customer?.status === '0'" type="info">
              {{ customer?.status_name || '无效' }}
            </el-tag>
            <span v-else>--</span>
          </span>
        </div>
        <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
          <span class="text-gray-500">销售归属：</span>
          <div class="flex items-center gap-2">
            {{ customer?.user_name || '--' }}
            <el-button type="primary" link>
              清除
            </el-button>
          </div>
        </div>
        <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
          <span class="text-gray-500">售后归属：</span>
          <div class="flex items-center gap-2">
            {{ customer?.after_user_name || '--' }}
            <el-button type="primary" link>
              请选择
            </el-button>
          </div>
        </div>
      </div>
      <!-- 标签页 -->
      <el-tabs v-model="activeDetailTab">
        <el-tab-pane label="跟进记录" name="follow">
          <div class="mb-4 rounded-lg bg-gray-50 p-4">
            <el-input v-model="followContent" type="textarea" :rows="4" placeholder="请输入跟进记录" show-word-limit :maxlength="200" />
            <div class="mt-4 flex items-center justify-between">
              <div class="flex items-center gap-4">
                <el-icon class="cursor-pointer text-xl text-gray-400">
                  <Picture />
                </el-icon>
                <el-checkbox v-model="autoFollowup">
                  只看员工主动记录的跟进
                </el-checkbox>
              </div>
              <div class="flex items-center gap-4">
                <el-date-picker v-model="nextFollowTime" type="datetime" placeholder="选择日期时间" />
                <el-button type="primary" class="!rounded-button" @click="publishFollow">
                  发布跟进
                </el-button>
              </div>
            </div>
          </div>
          <!-- 跟进记录列表 -->
          <div class="space-y-4">
            <div v-for="(record, index) in followRecords" :key="index" class="rounded-lg bg-gray-50 p-4">
              <div class="mb-2 flex items-center justify-between">
                <div class="text-gray-500">
                  {{ record.time }}
                </div>
                <el-dropdown>
                  <el-icon class="cursor-pointer">
                    <More />
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>编辑</el-dropdown-item>
                      <el-dropdown-item>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <div class="mb-2">
                {{ record.content }}
              </div>
              <div class="text-gray-500">
                下次跟进时间：{{ record.nextTime }}
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="客户信息" name="info">
          客户信息内容
        </el-tab-pane>
        <el-tab-pane label="合同记录" name="contract">
          合同记录内容
        </el-tab-pane>
        <el-tab-pane label="资料修改记录" name="changes">
          资料修改记录内容
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
  <!-- 新建线索弹窗 -->
  <el-dialog v-model="dialogVisible" title="新建线索" width="1000px" :close-on-click-modal="false">
    <div class="p-4">
      <div class="mb-6">
        <div class="mb-4 text-lg font-medium">
          创建客户线索
        </div>

        <!-- 第一行 -->
        <div class="grid grid-cols-3 mb-6 gap-6">
          <div>
            <div class="mb-2">
              所属门店
            </div>
            <el-select v-model="formData.store" placeholder="厦门小羽佳线索店" class="w-full">
              <el-option label="厦门小羽佳线索店" value="store1" />
            </el-select>
          </div>
          <div>
            <div class="mb-2">
              销售归属人
            </div>
            <el-select v-model="formData.salesOwner" placeholder="请选择销售归属人" class="w-full">
              <el-option label="请选择销售归属人" value="" />
            </el-select>
          </div>
          <div>
            <div class="mb-2">
              售后归属人
            </div>
            <el-select v-model="formData.serviceOwner" placeholder="请选择售后归属人" class="w-full">
              <el-option label="请选择售后归属人" value="" />
            </el-select>
          </div>
        </div>

        <!-- 类型选择 -->
        <div class="mb-6">
          <div class="mb-2 flex items-center">
            <span class="mr-1 text-red-500">*</span>
            <span>类型</span>
          </div>
          <div class="flex flex-wrap gap-3">
            <el-button
              v-for="type in typeOptions"
              :key="type.value"
              :type="formData.type === type.value ? 'primary' : ''"
              class="!rounded-button"
              @click="formData.type = type.value"
            >
              {{ type.label }}
            </el-button>
          </div>
        </div>

        <!-- 来源选择 -->
        <div class="mb-6">
          <div class="mb-2 flex items-center">
            <span class="mr-1 text-red-500">*</span>
            <span>来源</span>
          </div>
          <div class="flex flex-wrap gap-3">
            <el-button
              v-for="source in sourceOptions"
              :key="source.value"
              :type="formData.source === source.value ? 'primary' : ''"
              class="!rounded-button"
              @click="formData.source = source.value"
            >
              {{ source.label }}
            </el-button>
            <el-button class="!rounded-button" @click="">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>添加
            </el-button>
          </div>
        </div>

        <!-- 客户信息 -->
        <div class="mb-6">
          <div class="grid grid-cols-3 mb-4 gap-6">
            <div>
              <div class="mb-2 flex items-center">
                <span class="mr-1 text-red-500">*</span>
                <span>客户姓名</span>
              </div>
              <el-input v-model="formData.name" placeholder="请输入客户姓名" />
            </div>
            <div>
              <div class="mb-2">
                客户电话
              </div>
              <el-input v-model="formData.phone" placeholder="请输入客户电话" />
            </div>
            <div>
              <div class="mb-2">
                客户微信
              </div>
              <el-input v-model="formData.wechat" placeholder="请输入客户微信" />
            </div>
          </div>

          <!-- 薪资待遇 -->
          <div class="grid grid-cols-3 mb-4 gap-6">
            <div>
              <div class="mb-2 flex items-center">
                <span class="mr-1 text-red-500">*</span>
                <span>薪资待遇</span>
              </div>
              <div class="flex gap-2">
                <el-select v-model="formData.salary" placeholder="6000" class="w-1/3">
                  <el-option label="6000" value="6000" />
                </el-select>
                <el-select v-model="formData.salaryRange" placeholder="左右" class="w-1/3">
                  <el-option label="左右" value="左右" />
                </el-select>
                <el-select v-model="formData.salaryPeriod" placeholder="每月" class="w-1/3">
                  <el-option label="每月" value="每月" />
                </el-select>
              </div>
            </div>
          </div>

          <!-- 休息时间 -->
          <div class="mb-4 flex flex-wrap gap-3">
            <el-button
              v-for="rest in restOptions"
              :key="rest.value"
              :type="formData.restTime === rest.value ? 'primary' : ''"
              class="!rounded-button"
              @click="formData.restTime = rest.value"
            >
              {{ rest.label }}
            </el-button>
          </div>

          <!-- 备注信息 -->
          <div class="mb-4">
            <div class="mb-2">
              客户备注（分享时对外可见，如招聘、含单大厅）
            </div>
            <el-input v-model="formData.publicNote" type="textarea" :rows="4" placeholder="请输入客户备注" />
          </div>

          <div class="mb-4">
            <div class="mb-2">
              对内备注（仅内部员工可见）
            </div>
            <el-input v-model="formData.privateNote" type="textarea" :rows="4" placeholder="请输入对内备注" />
          </div>

          <!-- 地址信息 -->
          <div class="mb-4">
            <div class="mb-2">
              现住地址
            </div>
            <div class="flex gap-2">
              <el-select v-model="formData.province" placeholder="福建/厦门" class="w-1/4">
                <el-option label="福建/厦门" value="福建/厦门" />
              </el-select>
              <el-input v-model="formData.address" placeholder="请输入详细地址" class="w-2/3" />
              <el-button class="!rounded-button">
                搜索
              </el-button>
            </div>
          </div>
        </div>

        <!-- 增加用工需求 -->
        <div class="mb-6">
          <div class="mb-4 flex items-center text-lg font-medium">
            <div class="mr-2 h-8 w-4 bg-blue-500" />
            增加用工需求
          </div>

          <!-- 学历要求 -->
          <div class="mb-4">
            <div class="mb-2">
              学历要求：
            </div>
            <div class="flex flex-wrap gap-3">
              <el-button
                v-for="edu in educationOptions"
                :key="edu.value"
                :type="formData.education === edu.value ? 'primary' : ''"
                class="!rounded-button"
                @click="formData.education = edu.value"
              >
                {{ edu.label }}
              </el-button>
            </div>
          </div>

          <!-- 住家要求 -->
          <div class="mb-4">
            <div class="mb-2">
              住家要求：
            </div>
            <div class="flex flex-wrap gap-3">
              <el-button
                v-for="live in livingOptions"
                :key="live.value"
                :type="formData.living === live.value ? 'primary' : ''"
                class="!rounded-button"
                @click="formData.living = live.value"
              >
                {{ live.label }}
              </el-button>
            </div>
          </div>

          <!-- 性别要求 -->
          <div class="mb-4">
            <div class="mb-2">
              性别要求：
            </div>
            <div class="flex flex-wrap gap-3">
              <el-button
                v-for="gender in genderOptions"
                :key="gender.value"
                :type="formData.gender === gender.value ? 'primary' : ''"
                class="!rounded-button"
                @click="formData.gender = gender.value"
              >
                {{ gender.label }}
              </el-button>
            </div>
          </div>

          <!-- 年龄要求 -->
          <div class="mb-4">
            <div class="mb-2">
              年龄要求：
            </div>
            <div class="flex flex-wrap gap-3">
              <el-button
                v-for="age in ageOptions"
                :key="age.value"
                :type="formData.age === age.value ? 'primary' : ''"
                class="!rounded-button"
                @click="formData.age = age.value"
              >
                {{ age.label }}
              </el-button>
            </div>
          </div>

          <!-- 经验要求 -->
          <div class="mb-4">
            <div class="mb-2">
              经验要求：
            </div>
            <div class="flex flex-wrap gap-3">
              <el-button
                v-for="exp in experienceOptions"
                :key="exp.value"
                :type="formData.experience === exp.value ? 'primary' : ''"
                class="!rounded-button"
                @click="formData.experience = exp.value"
              >
                {{ exp.label }}
              </el-button>
            </div>
          </div>

          <!-- 增加客户情况 -->
          <div class="mb-6">
            <div class="mb-4 flex items-center text-lg font-medium">
              <div class="mr-2 h-8 w-4 bg-blue-500" />
              增加客户情况
            </div>

            <div class="grid grid-cols-3 mb-4 gap-6">
              <div>
                <div class="mb-2">
                  雇主家庭面积
                </div>
                <el-input v-model="formData.homeArea" placeholder="请输入雇主家庭面积" />
              </div>
              <div>
                <div class="mb-2">
                  客户紧急联系电话
                </div>
                <el-input v-model="formData.emergencyPhone" placeholder="请输入客户紧急联系电话" />
              </div>
              <div>
                <div class="mb-2">
                  预产期
                </div>
                <el-date-picker v-model="formData.expectedDate" type="date" placeholder="请选择预产期" class="w-full" />
              </div>
            </div>

            <div class="grid grid-cols-3 mb-4 gap-6">
              <div>
                <div class="mb-2">
                  宝宝生日
                </div>
                <el-date-picker v-model="formData.babyBirthday" type="date" placeholder="请选择宝宝生日" class="w-full" />
              </div>
              <div>
                <div class="mb-2">
                  雇主真实姓名
                </div>
                <el-input v-model="formData.employerRealName" placeholder="请输入雇主真实姓名" />
              </div>
              <div>
                <div class="mb-2">
                  雇主身份证号
                </div>
                <el-input v-model="formData.employerIdCard" placeholder="请输入雇主身份证号" />
              </div>
            </div>

            <div class="grid grid-cols-3 mb-4 gap-6">
              <div>
                <div class="mb-2">
                  雇主生日
                </div>
                <el-date-picker v-model="formData.employerBirthday" type="date" placeholder="请选择雇主生日" class="w-full" />
              </div>
              <div>
                <div class="mb-2">
                  雇主籍贯
                </div>
                <el-select v-model="formData.employerOrigin" placeholder="请选择" class="w-full">
                  <el-option label="请选择" value="" />
                </el-select>
              </div>
            </div>

            <div class="mb-4">
              <div class="mb-2">
                海外地址
              </div>
              <el-input v-model="formData.overseasAddress" placeholder="如客户在海外，在此输入地址" />
            </div>

            <div class="mb-4 flex justify-center">
              <el-button type="primary" link class="flex items-center" @click="showMoreInfo = !showMoreInfo">
                {{ showMoreInfo ? '收起更多信息' : '展开更多信息' }}
                <el-icon class="ml-1">
                  <component :is="showMoreInfo ? ArrowUp : ArrowDown" />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-4">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="submitForm">
          保存
        </el-button>
      </div>
    </div>
  </el-dialog>
  <!-- 添加合同对话框 -->
  <el-dialog v-model="showAddContractDialog" title="添加合同" width="700px" :close-on-click-modal="false">
    <div class="p-4">
      <div class="mb-6">
        <div class="grid grid-cols-2 mb-4 gap-6">
          <div>
            <div class="mb-2">
              合同标题
            </div>
            <el-input v-model="contractForm.title" placeholder="请输入合同标题" />
          </div>
          <div>
            <div class="mb-2">
              合同金额
            </div>
            <el-input-number v-model="contractForm.amount" :min="0" :precision="2" :step="100" class="w-full" />
          </div>
        </div>

        <div class="grid grid-cols-2 mb-4 gap-6">
          <div>
            <div class="mb-2">
              阿姨类型
            </div>
            <el-select v-model="contractForm.auntType" placeholder="请选择阿姨类型" class="w-full">
              <el-option v-for="type in auntTypeOptions" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
          </div>
          <div>
            <div class="mb-2">
              签约时间
            </div>
            <el-date-picker v-model="contractForm.signTime" type="datetime" placeholder="请选择签约时间" class="w-full" />
          </div>
        </div>

        <div class="grid grid-cols-2 mb-4 gap-6">
          <div>
            <div class="mb-2">
              服务开始时间
            </div>
            <el-date-picker v-model="contractForm.serviceStartTime" type="datetime" placeholder="请选择服务开始时间" class="w-full" />
          </div>
          <div>
            <div class="mb-2">
              服务结束时间
            </div>
            <el-date-picker v-model="contractForm.serviceEndTime" type="datetime" placeholder="请选择服务结束时间" class="w-full" />
          </div>
        </div>

        <div class="mb-4">
          <div class="mb-2">
            合同备注
          </div>
          <el-input v-model="contractForm.remark" type="textarea" :rows="3" placeholder="请输入合同备注" />
        </div>

        <div class="mb-4">
          <div class="mb-2">
            合同附件
          </div>
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :file-list="contractForm.files"
            :on-change="handleContractFileChange"
          >
            <el-button type="primary">
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                可上传合同扫描件或电子合同文件
              </div>
            </template>
          </el-upload>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAddContractDialog = false">取消</el-button>
        <el-button type="primary" @click="createContract">创建合同</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 添加标签对话框 -->
  <el-dialog v-model="showAddTagDialog" title="添加标签" width="500px" :close-on-click-modal="false">
    <div class="p-4">
      <div class="mb-4">
        <div class="mb-2">
          选择标签
        </div>
        <div class="flex flex-wrap gap-2">
          <el-tag
            v-for="tag in availableTags"
            :key="tag.uuid"
            :type="selectedTags.includes(tag.uuid) ? 'primary' : 'info'"
            class="mb-2 cursor-pointer !rounded-full"
            :style="{ backgroundColor: selectedTags.includes(tag.uuid) ? '' : tag.color }"
            @click="toggleTagSelection(tag.uuid)"
          >
            {{ tag.name }}
          </el-tag>
        </div>
      </div>
      <div class="mb-4">
        <div class="mb-2">
          创建新标签
        </div>
        <div class="flex gap-2">
          <el-input v-model="newTagName" placeholder="输入标签名称" />
          <el-color-picker v-model="newTagColor" />
          <el-button type="primary" class="!rounded-button" @click="createNewTag">
            创建
          </el-button>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAddTagDialog = false">取消</el-button>
        <el-button type="primary" @click="addTagsToCustomer">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="importDialogVisible" title="批量导入客户线索数据" width="500px" :close-on-click-modal="false">
    <div class="p-4">
      <el-upload
        class="upload-demo"
        drag
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-change="handleFileChange"
        :auto-upload="false"
        accept=".xlsx"
      >
        <el-button type="primary">
          选择文件
        </el-button>
        <span class="ml-2 text-gray-500">上传文件（格式xlsx，2MB以内）</span>
      </el-upload>
      <div class="mt-4">
        <div class="mb-2">
          设置数据归属账号（数据导入后，归属到该账号名下）
        </div>
        <el-select v-model="importAccount" placeholder="请选择" class="w-full">
          <el-option v-for="item in accountOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="mt-4 text-sm text-gray-500">
        <div>
          1. 请按照导入模板，整理好数据再上传录入。点击
          <el-link type="primary" @click="downloadTemplate">
            下载客户线索导入模板
          </el-link>
        </div>
        <div>2. 数据表限制20000条以内，文件大小限制2MB</div>
        <div>3. 客户线索数据中，手机号必填，已存在的数据（手机号一致），将不会导入。</div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleImport">确定导入</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 添加标签对话框 -->
  <el-dialog v-model="showAddTagDialog" title="添加标签" width="700px" :close-on-click-modal="false">
    <div class="p-4">
      <div class="mb-6">
        <div class="mb-2 font-medium">
          客户当前的标签
        </div>
        <div class="min-h-[60px] border border-gray-200 rounded p-4">
          <el-button v-if="!customer?.tags || customer.tags.length === 0" plain class="border-dashed">
            <span class="flex items-center">
              <i class="el-icon-plus mr-1">+</i>
              添加标签
            </span>
          </el-button>
          <div v-else class="flex flex-wrap gap-2">
            <el-tag
              v-for="tag in customer?.tags"
              :key="tag.uuid"
              class="mb-2"
              :style="{ backgroundColor: tag.color, color: '#fff', border: 'none' }"
              closable
              @close="removeTag(tag.uuid)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
        </div>
      </div>

      <div class="mb-6">
        <div class="mb-2 flex items-center justify-between">
          <div class="font-medium">
            可选标签（点击添加为客户的标签）
          </div>
          <el-button type="text" class="text-blue-500" @click="showTagManagement">
            管理标签
          </el-button>
        </div>
        <div class="flex flex-wrap gap-3">
          <div
            v-for="tag in availableTags"
            :key="tag.uuid"
            class="relative cursor-pointer border rounded px-4 py-2"
            :style="{ borderColor: tag.color, color: tag.color }"
            @click="toggleTagSelection(tag.uuid)"
          >
            {{ tag.name }}
            <div
              class="absolute h-5 w-5 flex items-center justify-center border rounded-full bg-white -right-2 -top-2"
              :style="{ borderColor: tag.color }"
            >
              <i class="text-xs" :style="{ color: tag.color }">+</i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAddTagDialog = false">取消</el-button>
        <el-button type="primary" @click="addTagsToCustomer">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import customerList from '@/api/modules/housekeeping/customer_list'
import { ArrowDown, ArrowUp, Close, CopyDocument, Document, More, Picture, Plus, Refresh, Search, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
// 弹窗控制
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const showMoreInfo = ref(true)
const showAddTagDialog = ref(false)
const showAddContractDialog = ref(false)

// 标签相关
const availableTags = ref([])
const selectedTags = ref([])
const newTagName = ref('')
const newTagColor = ref('#1890FF')

// 合同相关
const contractForm = reactive({
  title: '',
  amount: 0,
  auntType: '',
  signTime: '',
  serviceStartTime: '',
  serviceEndTime: '',
  remark: '',
  files: [],
})

// 阿姨类型选项
const auntTypeOptions = [
  { value: 'nanny', label: '保姆' },
  { value: 'matron', label: '月嫂' },
  { value: 'housekeeper', label: '家政服务' },
  { value: 'eldercare', label: '老人院' },
  { value: 'childcare', label: '育儿师' },
  { value: 'cook', label: '厨师' },
]

// 表单数据
const formData = reactive({
  store: 'store1',
  salesOwner: '',
  serviceOwner: '',
  type: '',
  source: '',
  name: '',
  phone: '',
  wechat: '',
  salary: '6000',
  salaryRange: '左右',
  salaryPeriod: '每月',
  restTime: '',
  publicNote: '',
  privateNote: '',
  province: '福建/厦门',
  address: '',
  education: '',
  living: '',
  gender: '',
  age: '',
  experience: '',
  homeArea: '',
  emergencyPhone: '',
  dueDate: '',
  // 增加客户情况字段
  expectedDate: '',
  babyBirthday: '',
  employerRealName: '',
  employerIdCard: '',
  employerBirthday: '',
  employerOrigin: '',
  overseasAddress: '',
})

// 选项数据
const typeOptions = [
  { label: '保姆', value: 'babysitter' },
  { label: '产后修复师', value: 'postpartum' },
  { label: '小儿推拿师', value: 'childMassage' },
  { label: '催乳师', value: 'lactation' },
  { label: '保安', value: 'security' },
  { label: '中医师', value: 'chineseMedicine' },
  { label: '家教', value: 'tutor' },
  { label: '心理理疗师', value: 'psychotherapy' },
  { label: '指导师', value: 'instructor' },
  { label: '家政员', value: 'housekeeper' },
  { label: '管家', value: 'butler' },
  { label: '厨师', value: 'chef' },
  { label: '月嫂', value: 'maternity' },
  { label: '育儿嫂', value: 'childcare' },
  { label: '老年护理', value: 'eldercare' },
  { label: '小时工', value: 'hourly' },
  { label: '白班阿姨', value: 'dayShift' },
  { label: '别墅家务', value: 'villa' },
  { label: '病人护理', value: 'patientCare' },
  { label: '育婴师', value: 'babyCare' },
  { label: '早教', value: 'earlyEducation' },
  { label: '其他', value: 'other' },
  { label: '单餐保姆', value: 'mealCook' },
]

const sourceOptions = [
  { label: '客户转介绍', value: 'referral' },
  { label: '公司400电话', value: '400' },
  { label: '系统分配', value: 'system' },
  { label: '公司指派', value: 'company' },
  { label: '渠道合作', value: 'channel' },
  { label: '小红书商务', value: 'xiaohongshu' },
  { label: 'UU跑腿', value: 'uu' },
  { label: '小红书笔记', value: 'xiaohongshuNote' },
  { label: '抖音私信', value: 'douyinDM' },
  { label: '小红书评论', value: 'xiaohongshuComment' },
  { label: '抖音短视频', value: 'douyinVideo' },
  { label: '超人妈妈', value: 'superMom' },
  { label: '百度地图', value: 'baiduMap' },
  { label: '小红书', value: 'xiaohongshuPlatform' },
  { label: '抖音', value: 'douyin' },
  { label: '微博心', value: 'weibo' },
  { label: '阿姨简历', value: 'resume' },
  { label: '微网站', value: 'microsite' },
  { label: '美团', value: 'meituan' },
  { label: '服务人员端小程序', value: 'miniProgram' },
]

const restOptions = [
  { label: '法定假', value: 'legal' },
  { label: '单休', value: 'oneDay' },
  { label: '双休', value: 'twoDay' },
  { label: '月休2天', value: 'twoPerMonth' },
  { label: '月休3天', value: 'threePerMonth' },
  { label: '月休4天', value: 'fourPerMonth' },
  { label: '月休6天', value: 'sixPerMonth' },
  { label: '月休8天', value: 'eightPerMonth' },
]

const educationOptions = [
  { label: '小学', value: 'primary' },
  { label: '初中', value: 'juniorHigh' },
  { label: '中专', value: 'vocational' },
  { label: '职高', value: 'vocationalHigh' },
  { label: '高中', value: 'highSchool' },
  { label: '大专', value: 'college' },
  { label: '本科', value: 'bachelor' },
  { label: '研究生及以上', value: 'master' },
]

const livingOptions = [
  { label: '住家', value: 'liveIn' },
  { label: '全天白班', value: 'fullDayShift' },
  { label: '上午白班', value: 'morningShift' },
  { label: '下午白班', value: 'afternoonShift' },
  { label: '钟点', value: 'hourly' },
  { label: '均可', value: 'any' },
]

const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '均可', value: 'any' },
]

const ageOptions = [
  { label: '不限', value: 'noLimit' },
  { label: '20-30岁', value: '20-30' },
  { label: '31-40岁', value: '31-40' },
  { label: '41-50岁', value: '41-50' },
  { label: '51-60岁', value: '51-60' },
  { label: '40岁以下', value: 'under40' },
  { label: '50岁以下', value: 'under50' },
  { label: '60岁以下', value: 'under60' },
]

const experienceOptions = [
  { label: '不限', value: 'noLimit' },
  { label: '一年以内（做过一点，大概知道）', value: 'lessThan1' },
  { label: '1-3年（入门级别）', value: '1-3' },
  { label: '3-5年（经验丰富）', value: '3-5' },
  { label: '5年以上（熟点没关系）', value: 'moreThan5' },
]

// 提交表单
async function submitForm() {
  try {
    // 验证必填字段
    if (!formData.name) {
      ElMessage.warning('请输入客户姓名')
      return
    }

    if (!formData.type) {
      ElMessage.warning('请选择类型')
      return
    }

    if (!formData.source) {
      ElMessage.warning('请选择来源')
      return
    }

    // 构建请求参数
    const customerData = {
      custom_name: formData.name,
      custom_mobile: formData.phone || '',
      store_uuid: formData.store === 'store1' ? 'store123' : formData.store, // 实际应使用真实的店铺 UUID
      status: '1', // 默认状态，实际应根据需求设置
      custom_source: formData.source,
      status_name: '待跟进', // 状态名称
      user_uuid: formData.salesOwner || '0', // 销售归属人
      after_user_uuid: formData.serviceOwner || '0', // 售后归属人
      aunt_type: formData.type, // 需求类型
      wechat_number: formData.wechat || '', // 微信
      min_salary: formData.salary || '', // 最低薪资
      salary_unit: formData.salaryPeriod || '', // 薪资单位
      vacation: formData.restTime || '', // 休息时间
      remark: formData.publicNote || '', // 备注
      work_demands: formData.privateNote || '', // 工作需求
      address: formData.address ? `${formData.province} ${formData.address}` : '', // 地址
      education: formData.education || '', // 学历
      can_live_home: formData.living || '', // 住家类型
      sex: formData.gender || '', // 性别
      min_age: formData.age ? formData.age.split('-')[0] : '', // 最小年龄
      max_age: formData.age ? formData.age.split('-')[1] : '', // 最大年龄
      min_experience: formData.experience || '', // 经验
      home_area: formData.homeArea || '', // 家庭面积
      contact_phone: formData.emergencyPhone || '', // 紧急联系电话
      due_date: formData.expectedDate ? formatDate(formData.expectedDate) : '', // 预产期
      baby_birthday: formData.babyBirthday ? formatDate(formData.babyBirthday) : '', // 宝宝生日
      id_number_name: formData.employerRealName || '', // 雇主真实姓名
      id_number: formData.employerIdCard || '', // 雇主身份证号
      id_number_birthday: formData.employerBirthday ? formatDate(formData.employerBirthday) : '', // 雇主生日
      hometown: formData.employerOrigin || '', // 雇主籍贯
      overseas_address: formData.overseasAddress || '', // 海外地址
    }

    // 调用API创建客户
    const response = await customerList.createCustom(customerData)

    if (response.code === 200) {
      ElMessage.success('创建客户成功')
      dialogVisible.value = false

      // 重新获取客户列表
      getCustomerList()

      // 重置表单
      resetForm()
    }
    else {
      ElMessage.error(response.msg || '创建客户失败')
    }
  }
  catch (error) {
    console.error('创建客户失败:', error)
    ElMessage.error('创建客户失败')
  }
}

// 重置搜索条件
function resetSearchForm() {
  // 重置基本搜索条件
  seachForm.value = {
    get_demand_filter: 1,
    rank: 0,
    main_status: -1,
    keyword: '',
    source: '',
    aunt_type: '',
    tag: '',
    store_uuid: '',
    sales_user_uuid: 0,
    after_user_uuid: 0,
    search_update_start_time: '',
    search_update_end_time: '',
    search_create_start_time: '',
    search_create_end_time: '',
    search_follow_start_time: '',
    search_follow_end_time: '',
  }

  // 重置选择项
  searchType.value = 'all'
  store.value = 'store1'
  salesOwner.value = 'unlimited'
  serviceOwner.value = 'unlimited'
  selectedTimeBtn.value = 'unlimited'
  dateRange.value = []
  selectedType.value = ''
  selectedTag.value = ''
  selectedSource.value = ''
  selectedCreateTime.value = ''
  selectedUpdateTime.value = ''
  // 重置索引
  selectedTypeIndex.value = -1
  selectedTagIndex.value = -1
  selectedSourceIndex.value = -1

  // 重置页码
  currentPage.value = 1

  // 重新获取客户列表
  getCustomerList()
}

// 重置表单
function resetForm() {
  formData.name = ''
  formData.phone = ''
  formData.wechat = ''
  formData.type = ''
  formData.source = ''
  formData.salesOwner = ''
  formData.serviceOwner = ''
  formData.salary = '6000'
  formData.salaryRange = '左右'
  formData.salaryPeriod = '每月'
  formData.restTime = ''
  formData.publicNote = ''
  formData.privateNote = ''
  formData.address = ''
  formData.education = ''
  formData.living = ''
  formData.gender = ''
  formData.age = ''
  formData.experience = ''
  formData.homeArea = ''
  formData.emergencyPhone = ''
  formData.expectedDate = ''
  formData.babyBirthday = ''
  formData.employerRealName = ''
  formData.employerIdCard = ''
  formData.employerBirthday = ''
  formData.employerOrigin = ''
  formData.overseasAddress = ''
}

const searchType = ref('all')
const store = ref('store1')
const salesOwner = ref('unlimited')
const serviceOwner = ref('unlimited')
const selectedTimeBtn = ref('unlimited')
// 新增时间按钮状态变量
const selectedCreateTimeBtn = ref('unlimited')
const selectedUpdateTimeBtn = ref('unlimited')
const showMoreFilters = ref(false)
const dateRange = ref([])
const createTimeRange = ref([])
const updateTimeRange = ref([])
const activeTab = ref('all')
const currentPage = ref(1)
const pageSize = ref(16)
const total = ref(0)
const searchFormList = ref([])
// 标签页计数
const tabCounts = ref({
  all: 562,
  pending: 278,
  following: 0,
  signed: 4,
  invalid: 280,
})

// 抽屉相关变量
const drawerVisible = ref(false)
const customer = ref(null)
const activeDetailTab = ref('info') // 默认显示基本信息标签
const followContent = ref('')
const nextFollowTime = ref('')
const autoFollowup = ref(false)
const followRecords = ref([])

// 标签相关变量已在上面定义
// 筛选条件选择数据
const selectedType = ref('')
const selectedTag = ref('')
const selectedSource = ref('')
const selectedCreateTime = ref('')
const selectedUpdateTime = ref('')
// 筛选条件索引
const selectedTypeIndex = ref(-1)
const selectedTagIndex = ref(-1)
const selectedSourceIndex = ref(-1)
const seachForm = ref({
  get_demand_filter: 1,
  rank: 0,
  main_status: -1,
  keyword: '',
  source: '',
  aunt_type: '',
  tag: '',
  store_uuid: '',
  sales_user_uuid: 0,
  after_user_uuid: 0,
  search_update_start_time: '',
  search_update_end_time: '',
  search_create_start_time: '',
  search_create_end_time: '',
  search_follow_start_time: '',
  search_follow_end_time: '',
})
const timeBtns = [
  { label: '不限', value: 'unlimited' },
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '明日', value: 'tomorrow' },
  { label: '后天', value: 'afterTomorrow' },
  { label: '本周', value: 'thisWeek' },
]
onMounted(() => {
  getCustomerList()
  getCustomFields()
})
const tableData = ref([])
// 获取客户列表
async function getCustomerList() {
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      mobile: seachForm.value.keyword && searchType.value === 'phone' ? seachForm.value.keyword : undefined,
      name: seachForm.value.keyword && searchType.value === 'name' ? seachForm.value.keyword : undefined,
      source: selectedSource.value || undefined,
      store_uuid: store.value !== 'store1' ? store.value : undefined,
      user_uuid: salesOwner.value !== 'unlimited' ? salesOwner.value : undefined,
      after_user_uuid: serviceOwner.value !== 'unlimited' ? serviceOwner.value : undefined,
      get_demand_filter: seachForm.value.get_demand_filter,
      rank: seachForm.value.rank,
      main_status: activeTab.value === 'all' ? -1 : getStatusByTab(activeTab.value),
      aunt_type: selectedType.value || undefined,
      tag: selectedTag.value || undefined,
    }

    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.search_follow_start_time = formatDate(dateRange.value[0])
      params.search_follow_end_time = formatDate(dateRange.value[1])
    }
    else if (selectedTimeBtn.value !== 'unlimited') {
      const { startTime, endTime } = getTimeRangeByButton(selectedTimeBtn.value)
      params.search_follow_start_time = startTime
      params.search_follow_end_time = endTime
    }

    // 处理创建时间
    if (selectedCreateTime.value !== 'unlimited') {
      const { startTime, endTime } = getTimeRangeByButton(selectedCreateTime.value)
      params.search_create_start_time = startTime
      params.search_create_end_time = endTime
    }

    // 处理更新时间
    if (selectedUpdateTime.value !== 'unlimited') {
      const { startTime, endTime } = getTimeRangeByButton(selectedUpdateTime.value)
      params.search_update_start_time = startTime
      params.search_update_end_time = endTime
    }

    // 调用API获取客户列表
    const response = await customerList.customerList(params)

    if (response.code === 200 && response.data) {
      // 使用API返回的数据填充表格
      tableData.value = response.data.records || []
      total.value = response.data.total || 0

      console.log('加载客户数据成功:', tableData.value)

      // 更新标签页计数
      tabCounts.value = {
        all: response.data.total || 0,
        pending: tableData.value.filter(item => item.status === '1').length,
        following: tableData.value.filter(item => item.status === '2').length,
        signed: tableData.value.filter(item => item.status === '3').length,
        invalid: tableData.value.filter(item => item.status === '0').length,
      }
    }
    else {
      ElMessage.error(response.msg || '获取客户列表失败')
      tableData.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
    tableData.value = []
    total.value = 0
  }
}

// 根据标签页获取状态值
function getStatusByTab(tab) {
  switch (tab) {
    case 'pending': return 1 // 待跟进
    case 'following': return 2 // 跟进中
    case 'signed': return 3 // 已签约
    case 'invalid': return 4 // 失效
    default: return -1 // 全部
  }
}

// 格式化日期
function formatDate(date) {
  if (!date)
    return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 根据按钮获取时间范围
function getTimeRangeByButton(button) {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  const afterTomorrow = new Date(today)
  afterTomorrow.setDate(afterTomorrow.getDate() + 2)

  // 计算本周的开始和结束
  const dayOfWeek = today.getDay() || 7 // 将周日的0转为7
  const weekStart = new Date(today)
  weekStart.setDate(today.getDate() - dayOfWeek + 1) // 周一
  const weekEnd = new Date(weekStart)
  weekEnd.setDate(weekStart.getDate() + 6) // 周日

  switch (button) {
    case 'today':
      return { startTime: formatDate(today), endTime: formatDate(today) }
    case 'yesterday':
      return { startTime: formatDate(yesterday), endTime: formatDate(yesterday) }
    case 'tomorrow':
      return { startTime: formatDate(tomorrow), endTime: formatDate(tomorrow) }
    case 'afterTomorrow':
      return { startTime: formatDate(afterTomorrow), endTime: formatDate(afterTomorrow) }
    case 'thisWeek':
      return { startTime: formatDate(weekStart), endTime: formatDate(weekEnd) }
    default:
      return { startTime: '', endTime: '' }
  }
}
async function getCustomFields() {
  try {
    const response = await customerList.getCustomFields()

    if ((response.code === 200 || response.code === 200) && response.data) {
      // 处理字段数据
      searchFormList.value = response.data || {}

      // 如果有需求字段，更新需求选项
      if (response.data.demand) {
        // 更新需求选项
      }

      // 如果有标签字段，更新标签选项
      if (response.data.tag) {
        // 更新标签选项
      }

      // 如果有来源字段，更新来源选项
      if (response.data.source) {
        // 更新来源选项
      }
    }
    else {
      ElMessage.error(response.msg || '获取客户字段失败')
      searchFormList.value = {}
    }
  }
  catch (error) {
    console.error('获取客户字段失败:', error)
    ElMessage.error('获取客户字段失败')
    searchFormList.value = {}
  }
}
function handleSelectionChange(val: any[]) {
  console.log('selected:', val)
}

// 处理下次跟进时间按钮点击
function handleFollowTimeClick(index: number, btn: any) {
  selectedTimeBtn.value = btn.value
  // 清空日期范围
  dateRange.value = []
}

// 获取客户等级标签类型
function getCustomerLevelType(level: string) {
  switch (level) {
    case 'A': return 'danger'
    case 'B': return 'warning'
    case 'C': return 'success'
    case 'D': return 'info'
    default: return 'info'
  }
}

// 查看跟进记录
function viewFollowRecords(row: any) {
  // 打开客户详情抽屉并切换到跟进记录标签
  openDrawer(row)
  activeDetailTab.value = 'follow'
}

// 计算日期差异天数
function calculateDaysDifference(dateStr: string): number {
  if (!dateStr)
    return Infinity

  const targetDate = new Date(dateStr)
  const today = new Date()

  // 计算日期差异（毫秒）
  const diffTime = targetDate.getTime() - today.getTime()
  // 转换为天数
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// 判断下次跟进时间是否临近（3天内）
function isFollowTimeNear(followTime: string) {
  const diffDays = calculateDaysDifference(followTime)
  // 如果在3天内或已过期，返回true
  return diffDays <= 3
}

// 判断预产期是否临近（30天内）
function isDueDateNear(dueDate: string) {
  const diffDays = calculateDaysDifference(dueDate)
  // 如果在30天内或已过期，返回true
  return diffDays <= 30
}

// 处理编辑标签
async function handleEditTags(row: any) {
  try {
    // 获取客户详情
    const response = await customerList.getCustomDetail(row.uuid)

    if (response.code === 200 && response.data) {
      // 更新客户详情
      customer.value = {
        ...customer.value,
        ...response.data,
      }

      // 获取客户标签
      await getCustomerTags(row.uuid)

      // 显示标签编辑弹窗
      showAddTagDialog.value = true
    }
    else {
      ElMessage.error(response.msg || '获取客户详情失败')
    }
  }
  catch (error) {
    console.error('获取客户详情失败:', error)
    ElMessage.error('获取客户详情失败')
  }
}

// 处理录入时间按钮点击
function handleCreateTimeClick(index: number, btn: any) {
  selectedCreateTimeBtn.value = btn.value
  // 清空日期范围
  createTimeRange.value = []
  // 更新查询参数
  selectedCreateTime.value = btn.value
}

// 处理更新时间按钮点击
function handleUpdateTimeClick(index: number, btn: any) {
  selectedUpdateTimeBtn.value = btn.value
  // 清空日期范围
  updateTimeRange.value = []
  // 更新查询参数
  selectedUpdateTime.value = btn.value
}

// 通用标签操作函数
function handleTagOperation(tagUuid: string, operation: 'toggle' | 'remove') {
  const index = selectedTags.value.indexOf(tagUuid)

  if (operation === 'toggle') {
    if (index === -1) {
      selectedTags.value.push(tagUuid)
    }
    else {
      selectedTags.value.splice(index, 1)
    }
  }
  else if (operation === 'remove' && index !== -1) {
    selectedTags.value.splice(index, 1)
  }
}

// 切换标签选中状态
function toggleTagSelection(tagUuid: string) {
  handleTagOperation(tagUuid, 'toggle')
}

// 移除标签
function removeTag(tagUuid: string) {
  handleTagOperation(tagUuid, 'remove')
}

// 显示标签管理
function showTagManagement() {
  ElMessage.info('标签管理功能将在后续版本中实现')
}

// 处理页面切换
function handlePageChange(page) {
  currentPage.value = page
  getCustomerList()
}

// 处理每页显示数量变化
function handleSizeChange(size) {
  pageSize.value = size
  // 切换每页显示数量时返回第一页
  currentPage.value = 1
  getCustomerList()
}
// 抽屉相关数据
async function openDrawer(row: any) {
  try {
    // 先设置基本信息并打开抽屉
    customer.value = row
    drawerVisible.value = true

    // 获取客户详情
    if (row && row.uuid) {
      const response = await customerList.getCustomDetail(row.uuid)

      if (response.code === 200 && response.data) {
        // 更新客户详情
        customer.value = {
          ...customer.value,
          ...response.data,
        }

        // 获取客户标签
        await getCustomerTags(row.uuid)

        // 获取客户跟进记录
        await getCustomerFollowRecords(row.uuid)

        // 获取客户合同列表
        await getCustomerContracts(row.uuid)
      }
      else {
        ElMessage.error(response.msg || '获取客户详情失败')
      }
    }
  }
  catch (error) {
    console.error('打开客户详情失败:', error)
    ElMessage.error('获取客户详情失败')
  }
}

// 获取客户标签
async function getCustomerTags(customerUuid) {
  try {
    if (!customerUuid)
      return

    const response = await customerList.getCustomTagList(customerUuid)

    if (response.code === 200 && response.data && response.data.list) {
      // 处理标签数据
      const tags = response.data.list.filter(tag => tag.is_selected).map(tag => ({
        uuid: tag.uuid,
        name: tag.name,
        color: tag.color,
      }))

      // 更新客户标签
      if (customer.value) {
        customer.value.tags = tags
      }

      // 保存所有可用标签
      availableTags.value = response.data.list.map(tag => ({
        uuid: tag.uuid,
        name: tag.name,
        color: tag.color,
        is_selected: tag.is_selected,
      }))

      // 初始化选中的标签
      selectedTags.value = response.data.list
        .filter(tag => tag.is_selected)
        .map(tag => tag.uuid)
    }
  }
  catch (error) {
    console.error('获取客户标签失败:', error)
  }
}

// 切换标签选中状态已在上面定义

// 创建新标签
async function createNewTag() {
  try {
    if (!newTagName.value) {
      ElMessage.warning('请输入标签名称')
      return
    }

    const response = await customerList.createTag(newTagName.value, newTagColor.value)

    if (response.code === 200 && response.data) {
      ElMessage.success('创建标签成功')

      // 添加新标签到可用标签列表
      const newTag = {
        uuid: response.data.uuid,
        name: response.data.name,
        color: response.data.color,
        is_selected: false,
      }
      availableTags.value.push(newTag)

      // 选中新标签
      selectedTags.value.push(newTag.uuid)

      // 重置表单
      newTagName.value = ''
    }
    else {
      ElMessage.error(response.msg || '创建标签失败')
    }
  }
  catch (error) {
    console.error('创建标签失败:', error)
    ElMessage.error('创建标签失败')
  }
}

// 为客户添加标签
async function addTagsToCustomer() {
  try {
    if (!customer.value || !customer.value.uuid) {
      ElMessage.warning('无法获取客户信息')
      return
    }

    // 获取当前已选中的标签
    const currentSelectedTags = availableTags.value
      .filter(tag => tag.is_selected)
      .map(tag => tag.uuid)

    // 需要添加的标签
    const tagsToAdd = selectedTags.value.filter(tagUuid => !currentSelectedTags.includes(tagUuid))

    // 需要移除的标签
    const tagsToRemove = currentSelectedTags.filter(tagUuid => !selectedTags.value.includes(tagUuid))

    // 添加标签
    for (const tagUuid of tagsToAdd) {
      await customerList.addCustomerTag(customer.value.uuid, tagUuid)
    }

    // 移除标签
    for (const tagUuid of tagsToRemove) {
      await customerList.removeCustomerTag(customer.value.uuid, tagUuid)
    }

    // 关闭对话框
    showAddTagDialog.value = false

    // 重新获取客户标签
    await getCustomerTags(customer.value.uuid)

    ElMessage.success('标签更新成功')
  }
  catch (error) {
    console.error('更新客户标签失败:', error)
    ElMessage.error('更新客户标签失败')
  }
}

// 获取客户跟进记录
async function getCustomerFollowRecords(customerUuid) {
  try {
    if (!customerUuid)
      return

    const response = await customerList.getCustomerFollowList(customerUuid)

    if (response.code === 200 && response.data && response.data.list) {
      // 处理跟进记录数据
      followRecords.value = response.data.list.map(item => ({
        id: item.id || item.uuid,
        time: `${item.create_time} ${item.create_user_name || ''}`,
        content: item.content || '',
        nextTime: item.follow_time || '',
        status: item.status,
        status_name: item.status_name,
      }))
    }
    else {
      followRecords.value = []
    }
  }
  catch (error) {
    console.error('获取客户跟进记录失败:', error)
    followRecords.value = []
  }
}

// 获取客户合同列表
async function getCustomerContracts(customerUuid) {
  try {
    if (!customerUuid)
      return

    const response = await customerList.getCustomContractList(customerUuid)

    if (response.code === 200 && response.data && response.data.list) {
      // 处理合同数据
      // 如果需要在详情页显示合同列表，可以在这里设置
    }
  }
  catch (error) {
    console.error('获取客户合同列表失败:', error)
  }
}

// 处理合同文件变化
function handleContractFileChange(file, fileList) {
  contractForm.files = fileList
}

// 创建合同
async function createContract() {
  try {
    if (!contractForm.title) {
      ElMessage.warning('请输入合同标题')
      return
    }

    if (!contractForm.amount) {
      ElMessage.warning('请输入合同金额')
      return
    }

    if (!contractForm.auntType) {
      ElMessage.warning('请选择阿姨类型')
      return
    }

    if (!contractForm.signTime) {
      ElMessage.warning('请选择签约时间')
      return
    }

    if (!contractForm.serviceStartTime || !contractForm.serviceEndTime) {
      ElMessage.warning('请选择服务开始和结束时间')
      return
    }

    if (!customer.value || !customer.value.uuid) {
      ElMessage.warning('无法获取客户信息')
      return
    }

    // 构建请求参数
    const contractData = {
      customer_uuid: customer.value.uuid,
      title: contractForm.title,
      amount: contractForm.amount,
      aunt_type: contractForm.auntType,
      sign_time: formatDateTime(contractForm.signTime),
      service_start_time: formatDateTime(contractForm.serviceStartTime),
      service_end_time: formatDateTime(contractForm.serviceEndTime),
      remark: contractForm.remark || '',
      files: contractForm.files.map(file => ({
        name: file.name,
        url: file.url || '',
      })),
    }

    // 调用API创建合同
    const response = await customerList.createCustomContract(contractData)

    if (response.code === 200) {
      ElMessage.success('创建合同成功')
      showAddContractDialog.value = false

      // 重新获取客户合同列表
      await getCustomerContracts(customer.value.uuid)

      // 重置表单
      resetContractForm()
    }
    else {
      ElMessage.error(response.msg || '创建合同失败')
    }
  }
  catch (error) {
    console.error('创建合同失败:', error)
    ElMessage.error('创建合同失败')
  }
}

// 重置合同表单
function resetContractForm() {
  contractForm.title = ''
  contractForm.amount = 0
  contractForm.auntType = ''
  contractForm.signTime = ''
  contractForm.serviceStartTime = ''
  contractForm.serviceEndTime = ''
  contractForm.remark = ''
  contractForm.files = []
}

// 格式化日期时间
function formatDateTime(date) {
  if (!date)
    return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 通用API响应处理函数
function handleApiResponse(response, successCallback, errorMessage) {
  if (response.code === 200 && response.data) {
    if (successCallback)
      successCallback(response.data)
    return true
  }
  else {
    ElMessage.error(response.msg || errorMessage)
    return false
  }
}

// 确认删除客户
function confirmDeleteCustomer() {
  if (!customer.value || !customer.value.uuid) {
    ElMessage.warning('无法获取客户信息')
    return
  }

  ElMessageBox.confirm(
    `确定要删除客户 ${customer.value.name || ''} 吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      deleteCustomer(customer.value.uuid)
    })
    .catch(() => {
      // 取消删除
    })
}

// 删除客户
async function deleteCustomer(uuid) {
  try {
    const response = await customerList.deleteCustom(uuid)

    if (response.code === 200) {
      ElMessage.success('删除客户成功')

      // 关闭抽屉
      drawerVisible.value = false

      // 重新获取客户列表
      getCustomerList()
    }
    else {
      ElMessage.error(response.msg || '删除客户失败')
    }
  }
  catch (error) {
    console.error('删除客户失败:', error)
    ElMessage.error('删除客户失败')
  }
}

// 发布跟进
async function publishFollow() {
  try {
    if (!followContent.value) {
      ElMessage.warning('请输入跟进内容')
      return
    }

    if (!customer.value || !customer.value.uuid) {
      ElMessage.warning('无法获取客户信息')
      return
    }

    // 构建请求参数
    const followData = {
      custom_uuid: customer.value.uuid,
      status: 1, // 默认状态，实际应根据需求设置
      status_name: '跟进中', // 状态名称
      content: followContent.value,
      follow_time: nextFollowTime.value ? new Date(nextFollowTime.value).toISOString() : '',
    }

    // 调用API添加跟进记录
    const response = await customerList.addCustomerFollow(followData)

    if (response.code === 200) {
      ElMessage.success('添加跟进记录成功')

      // 重新获取跟进记录
      await getCustomerFollowRecords(customer.value.uuid)

      // 重置表单
      followContent.value = ''
      nextFollowTime.value = ''
    }
    else {
      ElMessage.error(response.msg || '添加跟进记录失败')
    }
  }
  catch (error) {
    console.error('添加跟进记录失败:', error)
    ElMessage.error('添加跟进记录失败')
  }
}
// 示例跟进记录已在上面定义
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
  border-radius: 20px;
}

.el-select :deep(.el-input__wrapper) {
  border-radius: 20px;
}

.el-date-picker {
  --el-date-editor-width: auto;
}

.el-button {
  height: 32px;
  padding: 0 16px;
}

.el-table {
  overflow: hidden;
  border-radius: 8px;
}
</style>
