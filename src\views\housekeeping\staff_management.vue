<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">员工管理</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">员工角色</span>
            <el-select v-model="selectedRole" placeholder="选择角色" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">门店</span>
            <el-select v-model="selectedStore" placeholder="选择门店" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="store in storeList"
                :key="store.uuid"
                :label="store.name"
                :value="store.uuid"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">员工状态</span>
            <el-select v-model="selectedStatus" placeholder="选择状态" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option label="在职" value="active" />
              <el-option label="离职" value="inactive" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">关键词</span>
            <el-input v-model="keyword" placeholder="员工姓名/手机号" class="w-[200px]" />
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap" @click="openCreateDialog">
              新增员工
            </el-button>
            <el-button type="info" class="!rounded-button whitespace-nowrap" @click="exportData">
              导出
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" class="w-full">
        <el-table-column width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar || defaultAvatar" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="roleName" label="角色" width="120" />
        <el-table-column prop="storeName" label="所属门店" width="150" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            <span>{{ scope.row.gender === 'male' ? '男' : '女' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="entryDate" label="入职日期" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看</el-button>
            <el-button type="warning" link @click="editStaff(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="confirmDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 员工详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="员工详情" width="800px">
      <div v-if="detailLoading" class="flex justify-center items-center py-20">
        <el-spinner size="large" />
      </div>
      <div v-else class="flex">
        <div class="w-1/3 flex flex-col items-center">
          <el-avatar :size="120" :src="staffDetail.avatar || defaultAvatar" class="mb-4" />
          <h3 class="text-xl font-bold mb-2">{{ staffDetail.name }}</h3>
          <p class="text-gray-500">{{ staffDetail.roleName }}</p>
        </div>
        <div class="w-2/3">
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">手机号</span>
              <span class="font-medium">{{ staffDetail.phone }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">性别</span>
              <span class="font-medium">{{ staffDetail.gender === 'male' ? '男' : '女' }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">所属门店</span>
              <span class="font-medium">{{ staffDetail.storeName }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">状态</span>
              <span class="font-medium">
                <el-tag :type="staffDetail.status === 'active' ? 'success' : 'danger'">
                  {{ staffDetail.status === 'active' ? '在职' : '离职' }}
                </el-tag>
              </span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">入职日期</span>
              <span class="font-medium">{{ staffDetail.entryDate }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">离职日期</span>
              <span class="font-medium">{{ staffDetail.leaveDate || '无' }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">身份证号</span>
              <span class="font-medium">{{ staffDetail.idCard || '无' }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">紧急联系人</span>
              <span class="font-medium">{{ staffDetail.emergencyContact || '无' }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">紧急联系人电话</span>
              <span class="font-medium">{{ staffDetail.emergencyPhone || '无' }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-gray-500 text-sm">创建时间</span>
              <span class="font-medium">{{ staffDetail.createTime }}</span>
            </div>
          </div>

          <div class="mb-6">
            <span class="text-gray-500 text-sm">家庭住址</span>
            <div class="mt-1 p-3 border rounded-lg">
              {{ staffDetail.address || '无' }}
            </div>
          </div>

          <div class="mb-6">
            <span class="text-gray-500 text-sm">备注</span>
            <div class="mt-1 p-3 border rounded-lg">
              {{ staffDetail.remark || '无' }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增/编辑员工对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEdit ? '编辑员工信息' : '新增员工'"
      width="800px"
    >
      <el-form
        ref="staffFormRef"
        :model="staffForm"
        :rules="staffRules"
        label-width="100px"
      >
        <div class="flex mb-6">
          <div class="w-1/3 flex flex-col items-center">
            <el-avatar :size="120" :src="avatarUrl || defaultAvatar" class="mb-4" />
            <el-upload
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleAvatarChange"
            >
              <el-button type="primary">上传头像</el-button>
            </el-upload>
          </div>
          <div class="w-2/3">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="staffForm.name" placeholder="请输入员工姓名" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="staffForm.phone" placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="staffForm.gender">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>

        <el-form-item label="角色" prop="roleId">
          <el-select v-model="staffForm.roleId" placeholder="请选择角色" class="w-full">
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属门店" prop="storeUuid">
          <el-select v-model="staffForm.storeUuid" placeholder="请选择门店" class="w-full">
            <el-option
              v-for="store in storeList"
              :key="store.uuid"
              :label="store.name"
              :value="store.uuid"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="staffForm.status">
            <el-radio label="active">在职</el-radio>
            <el-radio label="inactive">离职</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="入职日期" prop="entryDate">
          <el-date-picker v-model="staffForm.entryDate" type="date" placeholder="请选择入职日期" class="w-full" />
        </el-form-item>
        <el-form-item label="离职日期" v-if="staffForm.status === 'inactive'">
          <el-date-picker v-model="staffForm.leaveDate" type="date" placeholder="请选择离职日期" class="w-full" />
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input v-model="staffForm.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="家庭住址">
          <el-input v-model="staffForm.address" placeholder="请输入家庭住址" />
        </el-form-item>
        <el-form-item label="紧急联系人">
          <el-input v-model="staffForm.emergencyContact" placeholder="请输入紧急联系人" />
        </el-form-item>
        <el-form-item label="紧急电话">
          <el-input v-model="staffForm.emergencyPhone" placeholder="请输入紧急联系人电话" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="staffForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="formSubmitting">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除确认" width="400px">
      <p>确定要删除员工 "{{ deleteStaff.name }}" 吗？</p>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteStaffConfirm" :loading="deleteLoading">确定删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import staffManagementApi from '@/api/modules/housekeeping/staff_management';

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

// 筛选条件
const selectedRole = ref('all');
const selectedStore = ref('all');
const selectedStatus = ref('all');
const keyword = ref('');

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);
const formSubmitting = ref(false);
const deleteLoading = ref(false);

// 列表数据
const roleList = ref([]);
const storeList = ref([]);
const tableData = ref([]);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 详情对话框
const detailDialogVisible = ref(false);
const staffDetail = ref({});

// 表单对话框
const formDialogVisible = ref(false);
const isEdit = ref(false);
const staffFormRef = ref(null);
const avatarUrl = ref('');
const avatarFile = ref(null);

// 删除对话框
const deleteDialogVisible = ref(false);
const deleteStaff = ref({});

// 表单数据
const staffForm = reactive({
  id: '',
  name: '',
  phone: '',
  gender: 'male',
  roleId: '',
  storeUuid: '',
  status: 'active',
  entryDate: '',
  leaveDate: '',
  idCard: '',
  address: '',
  emergencyContact: '',
  emergencyPhone: '',
  remark: '',
  avatar: ''
});

// 表单验证规则
const staffRules = {
  name: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  storeUuid: [
    { required: true, message: '请选择所属门店', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  entryDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ]
};

// 获取员工角色列表
async function getRoleList() {
  try {
    const response = await staffManagementApi.getRoleList();

    if (response.code === 200 && response.data) {
      roleList.value = response.data.list || [];
    } else {
      roleList.value = [];
    }
  } catch (error) {
    console.error('获取员工角色列表失败:', error);
    roleList.value = [];
  }
}

// 获取门店列表
async function getStoreList() {
  try {
    const response = await staffManagementApi.getStoreList();

    if (response.code === 200 && response.data) {
      storeList.value = response.data.list || [];
    } else {
      storeList.value = [];
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    storeList.value = [];
  }
}

// 获取员工列表
async function getStaffList() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: keyword.value || undefined,
      role_id: selectedRole.value === 'all' ? undefined : selectedRole.value,
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value
    };

    const response = await staffManagementApi.getStaffList(params);

    if (response.code === 200 && response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || '获取员工列表失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取员工列表失败:', error);
    ElMessage.error('获取员工列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 获取员工详情
async function getStaffDetail(staffId) {
  try {
    detailLoading.value = true;

    const response = await staffManagementApi.getStaffDetail(staffId);

    if (response.code === 200 && response.data) {
      staffDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || '获取员工详情失败');
      staffDetail.value = {};
    }
  } catch (error) {
    console.error('获取员工详情失败:', error);
    ElMessage.error('获取员工详情失败');
    staffDetail.value = {};
  } finally {
    detailLoading.value = false;
  }
}

// 查看员工详情
function viewDetail(row) {
  detailDialogVisible.value = true;
  getStaffDetail(row.id);
}

// 打开新增员工对话框
function openCreateDialog() {
  isEdit.value = false;
  resetForm();
  formDialogVisible.value = true;
}

// 编辑员工信息
function editStaff(row) {
  isEdit.value = true;
  resetForm();

  // 获取员工详情
  getStaffDetail(row.id).then(() => {
    // 填充表单数据
    staffForm.id = staffDetail.value.id;
    staffForm.name = staffDetail.value.name;
    staffForm.phone = staffDetail.value.phone;
    staffForm.gender = staffDetail.value.gender;
    staffForm.roleId = staffDetail.value.roleId;
    staffForm.storeUuid = staffDetail.value.storeUuid;
    staffForm.status = staffDetail.value.status;
    staffForm.entryDate = staffDetail.value.entryDate;
    staffForm.leaveDate = staffDetail.value.leaveDate;
    staffForm.idCard = staffDetail.value.idCard;
    staffForm.address = staffDetail.value.address;
    staffForm.emergencyContact = staffDetail.value.emergencyContact;
    staffForm.emergencyPhone = staffDetail.value.emergencyPhone;
    staffForm.remark = staffDetail.value.remark;
    staffForm.avatar = staffDetail.value.avatar;

    // 设置头像预览
    if (staffDetail.value.avatar) {
      avatarUrl.value = staffDetail.value.avatar;
    }

    formDialogVisible.value = true;
  });
}

// 重置表单
function resetForm() {
  if (staffFormRef.value) {
    staffFormRef.value.resetFields();
  }

  staffForm.id = '';
  staffForm.name = '';
  staffForm.phone = '';
  staffForm.gender = 'male';
  staffForm.roleId = '';
  staffForm.storeUuid = '';
  staffForm.status = 'active';
  staffForm.entryDate = '';
  staffForm.leaveDate = '';
  staffForm.idCard = '';
  staffForm.address = '';
  staffForm.emergencyContact = '';
  staffForm.emergencyPhone = '';
  staffForm.remark = '';
  staffForm.avatar = '';

  avatarUrl.value = '';
  avatarFile.value = null;
}

// 处理头像变化
function handleAvatarChange(file) {
  avatarFile.value = file.raw;
  avatarUrl.value = URL.createObjectURL(file.raw);
}

// 上传头像
async function uploadAvatar() {
  if (!avatarFile.value) {
    return staffForm.avatar;
  }

  try {
    const formData = new FormData();
    formData.append('file', avatarFile.value);

    const response = await staffManagementApi.uploadStaffAvatar(formData);

    if (response.code === 200 && response.data) {
      return response.data.url;
    } else {
      throw new Error(response.msg || '上传头像失败');
    }
  } catch (error) {
    console.error('上传头像失败:', error);
    ElMessage.error('上传头像失败');
    return staffForm.avatar;
  }
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';

  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

// 提交表单
async function submitForm() {
  if (!staffFormRef.value) return;

  try {
    await staffFormRef.value.validate();

    formSubmitting.value = true;

    // 上传头像
    const avatarUrl = await uploadAvatar();

    // 构建请求数据
    const data = {
      name: staffForm.name,
      phone: staffForm.phone,
      gender: staffForm.gender,
      role_id: staffForm.roleId,
      store_uuid: staffForm.storeUuid,
      status: staffForm.status,
      entry_date: formatDate(staffForm.entryDate),
      id_card: staffForm.idCard || undefined,
      address: staffForm.address || undefined,
      emergency_contact: staffForm.emergencyContact || undefined,
      emergency_phone: staffForm.emergencyPhone || undefined,
      remark: staffForm.remark || undefined,
      avatar: avatarUrl || undefined
    };

    // 如果是离职状态，添加离职日期
    if (staffForm.status === 'inactive' && staffForm.leaveDate) {
      data.leave_date = formatDate(staffForm.leaveDate);
    }

    let response;

    if (isEdit.value) {
      // 编辑员工
      data.id = staffForm.id;
      response = await staffManagementApi.updateStaff(data);
    } else {
      // 新增员工
      response = await staffManagementApi.createStaff(data);
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '编辑员工信息成功' : '新增员工成功');
      formDialogVisible.value = false;
      getStaffList();
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '编辑员工信息失败' : '新增员工失败'));
    }
  } catch (error) {
    console.error(isEdit.value ? '编辑员工信息失败:' : '新增员工失败:', error);
    ElMessage.error(isEdit.value ? '编辑员工信息失败' : '新增员工失败');
  } finally {
    formSubmitting.value = false;
  }
}

// 确认删除员工
function confirmDelete(row) {
  deleteStaff.value = row;
  deleteDialogVisible.value = true;
}

// 删除员工
async function deleteStaffConfirm() {
  try {
    deleteLoading.value = true;

    const response = await staffManagementApi.deleteStaff(deleteStaff.value.id);

    if (response.code === 200) {
      ElMessage.success('删除员工成功');
      deleteDialogVisible.value = false;
      getStaffList();
    } else {
      ElMessage.error(response.msg || '删除员工失败');
    }
  } catch (error) {
    console.error('删除员工失败:', error);
    ElMessage.error('删除员工失败');
  } finally {
    deleteLoading.value = false;
  }
}

// 导出员工数据
async function exportData() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      keyword: keyword.value || undefined,
      role_id: selectedRole.value === 'all' ? undefined : selectedRole.value,
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value
    };

    const response = await staffManagementApi.exportStaffData(params);

    // 创建一个下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `员工数据_${new Date().getTime()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出员工数据失败:', error);
    ElMessage.error('导出员工数据失败');
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function handleReset() {
  selectedRole.value = 'all';
  selectedStore.value = 'all';
  selectedStatus.value = 'all';
  keyword.value = '';

  handleSearch();
}

// 查询数据
function handleSearch() {
  currentPage.value = 1;
  getStaffList();
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
  getStaffList();
}

function handleCurrentChange(val) {
  currentPage.value = val;
  getStaffList();
}

// 页面加载时获取数据
onMounted(() => {
  Promise.all([
    getRoleList(),
    getStoreList()
  ]).then(() => {
    getStaffList();
  });
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
