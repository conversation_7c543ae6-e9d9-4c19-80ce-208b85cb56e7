<template>
  <div class="admin-settings">
    <el-card>
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-form-item label="角色" prop="role" >
          <el-select v-model="formData.role" :disabled="true" placeholder="请选择角色" readonly>
            <el-option label="管理员" value="admin" />
            <el-option label="店长" value="manager" />
            <el-option label="客服" value="service" />
          </el-select>
        </el-form-item>

        <el-form-item label="所属门店" prop="store">
          <el-select v-model="formData.store" placeholder="请选择门店">
            <el-option label="厦门小羽佳线索店" value="xmxyj" />
            <!-- 后续对接门店数据接口 -->
          </el-select>
        </el-form-item>

        <el-form-item label="职称" prop="title" :disabled="true">
          <el-input v-model="formData.title" placeholder="请输入职称" disabled />
        </el-form-item>

        <el-form-item label="姓名" prop="name" :disabled="true">
          <el-input v-model="formData.name" placeholder="请输入姓名" disabled />
        </el-form-item>

        <el-form-item label="手机号" prop="phone" :disabled="true">
          <el-input v-model="formData.phone" placeholder="请输入手机号" disabled />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="formData.password" 
            type="password"
            show-password
            placeholder="如需修改，请填写新的登录密码"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">保存信息</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const formData = reactive({
  role: 'amdin',
  store: 'xmxyj',
  title: 'amin',
  name: '魏德建',
  phone: '15750773952',
  password: '',
  remark: ''
})

const validatePhone = (rule: any, value: string, callback: Function) => {
  if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

const validatePassword = (rule: any, value: string, callback: Function) => {
  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else if (!/[A-Za-z]/.test(value) || !/\d/.test(value)) {
    callback(new Error('需包含字母和数字组合'))
  } else {
    callback()
  }
}

const formRules = reactive<FormRules>({
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  store: [{ required: true, message: '请选择所属门店', trigger: 'change' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ]
})

const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      // 实际提交逻辑需对接后端API
      ElMessage.success('提交成功')
      resetForm()
    }
  })
}

const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style scoped>
.admin-settings {
  padding: 20px;
}

.el-card {
  margin-top: 20px;
}

.el-form {
  max-width: 600px;
  margin: 0 auto;
}

.el-input,
.el-select,
.el-textarea {
  width: 100%;
}

@media (width <= 768px) {
  .el-form-item__label {
    margin-bottom: 8px;
    text-align: left;
  }
}
</style>