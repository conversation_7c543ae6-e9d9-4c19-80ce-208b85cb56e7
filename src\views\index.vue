<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { Document, Folder, Ticket } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const route = useRouter()
function goToCreateOrder() {
  route.push('/home_business/create_order')
}
const pendingOrders = ref(1)
const stats = ref({
  unaccepted: 1,
  late: 0,
  overtime: 0,
  unpaid: 0,
})
const businessStats = ref({
  todayOrders: 156,
  todayIncome: 18650,
  monthOrders: 3264,
})

const monthlyChartRef = ref<HTMLElement>()

onMounted(() => {
  const chart = echarts.init(monthlyChartRef.value!)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLabel: {
        color: '#6B7280',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLabel: {
        color: '#6B7280',
      },
    },
    series: [
      {
        name: '订单量',
        type: 'bar',
        barWidth: '30%',
        itemStyle: {
          color: '#60A5FA',
        },
        data: [2200, 2800, 3200, 3600, 3100, 3800, 4200, 3900, 4500, 4200, 4800, 5100],
      },
    ],
  }
  chart.setOption(option)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="mx-auto max-w-[1440px] flex gap-6">
      <!-- 左侧内容区 -->
      <div class="flex-1">
        <!-- 小程序派单提醒 -->
        <div class="mb-6 rounded-lg bg-white p-6">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium">
              小程序派单提醒 (待派单 {{ pendingOrders }})
            </h2>
            <el-link type="primary">
              查看全部 >
            </el-link>
          </div>
          <div v-if="pendingOrders === 0" class="flex flex-col items-center justify-center py-12">
            <el-icon class="mb-4 text-4xl text-gray-300">
              <Folder />
            </el-icon>
            <span class="text-gray-500">暂无数据</span>
          </div>
          <div v-else class="border border-gray-100 rounded-lg p-4">
            <div class="mb-2 flex items-center justify-between text-sm text-gray-600">
              <span>2025-03-13 08:00-08:01</span>
              <span>空调安装 - 挂机拆装</span>
              <span>13636909945</span>
              <span class="text-gray-500">福建省厦门市思明区仙岳山隧道</span>
            </div>
          </div>
        </div>
        <!-- 异常派单 -->
        <div class="rounded-lg bg-white p-6">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium">
              异常派单 (昨天/今天/明天)
            </h2>
            <el-link type="primary">
              查看全部 >
            </el-link>
          </div>
          <div class="grid grid-cols-4 mb-6 gap-4">
            <div class="flex cursor-pointer items-center border border-blue-100 rounded-lg p-4 hover:border-blue-200">
              <div class="flex-1">
                <div class="text-sm text-gray-500">
                  服务人员未接单
                </div>
                <div class="text-xl text-blue-500 font-medium">
                  {{ stats.unaccepted }}
                </div>
              </div>
            </div>
            <div class="flex cursor-pointer items-center border border-blue-100 rounded-lg p-4 hover:border-blue-200">
              <div class="flex-1">
                <div class="text-sm text-gray-500">
                  服务人员迟到
                </div>
                <div class="text-xl text-blue-500 font-medium">
                  {{ stats.late }}
                </div>
              </div>
            </div>
            <div class="flex cursor-pointer items-center border border-blue-100 rounded-lg p-4 hover:border-blue-200">
              <div class="flex-1">
                <div class="text-sm text-gray-500">
                  服务人员超时
                </div>
                <div class="text-xl text-blue-500 font-medium">
                  {{ stats.overtime }}
                </div>
              </div>
            </div>
            <div class="flex cursor-pointer items-center border border-blue-100 rounded-lg p-4 hover:border-blue-200">
              <div class="flex-1">
                <div class="text-sm text-gray-500">
                  未收款
                </div>
                <div class="text-xl text-blue-500 font-medium">
                  {{ stats.unpaid }}
                </div>
              </div>
            </div>
          </div>
          <div v-if="stats.unaccepted > 0" class="border border-gray-100 rounded-lg p-4">
            <div class="mb-2 text-base font-medium">
              叶子 (13636909945)
            </div>
            <div class="flex items-center justify-between text-sm text-gray-600">
              <span>2025-03-13 08:00-08:01</span>
              <span>13636909945</span>
              <span class="text-gray-500">福建省厦门市思明区仙岳山隧道</span>
            </div>
          </div>
        </div>
        <!-- 业务数据 -->
        <div class="mb-6 rounded-lg bg-white p-6">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium">
              业务数据
            </h2>
            <el-link type="primary">
              查看全部 >
            </el-link>
          </div>
          <div class="grid grid-cols-3 gap-4">
            <div class="border border-gray-100 rounded-lg p-4">
              <div class="mb-2 text-sm text-gray-500">
                今日订单数
              </div>
              <div class="text-xl text-gray-900 font-medium">
                {{ businessStats.todayOrders }}
              </div>
              <div class="mt-2 text-xs text-gray-400">
                较昨日 +12%
              </div>
            </div>
            <div class="border border-gray-100 rounded-lg p-4">
              <div class="mb-2 text-sm text-gray-500">
                今日收入
              </div>
              <div class="text-xl text-gray-900 font-medium">
                ¥{{ businessStats.todayIncome }}
              </div>
              <div class="mt-2 text-xs text-gray-400">
                较昨日 +8%
              </div>
            </div>
            <div class="border border-gray-100 rounded-lg p-4">
              <div class="mb-2 text-sm text-gray-500">
                本月订单数
              </div>
              <div class="text-xl text-gray-900 font-medium">
                {{ businessStats.monthOrders }}
              </div>
              <div class="mt-2 text-xs text-gray-400">
                较上月 +15%
              </div>
            </div>
          </div>
        </div>
        <!-- 月度数据统计 -->
        <div class="mb-6 rounded-lg bg-white p-6">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium">
              月度数据统计
            </h2>
            <el-link type="primary">
              查看全部 >
            </el-link>
          </div>
          <div ref="monthlyChartRef" class="h-[300px] w-full" />
        </div>
        <!-- 服务人员排行榜 -->
        <div class="rounded-lg bg-white p-6">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium">
              服务人员排行榜
            </h2>
            <el-link type="primary">
              查看全部 >
            </el-link>
          </div>
          <div class="space-y-4">
            <div class="flex items-center justify-between border border-gray-100 rounded-lg p-4">
              <div class="flex items-center gap-4">
                <div class="h-8 w-8 flex items-center justify-center rounded-full bg-yellow-500 text-white">
                  1
                </div>
                <div class="flex-1">
                  <div class="font-medium">
                    张伟明
                  </div>
                  <div class="text-sm text-gray-500">
                    完成订单: 128
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-blue-500 font-medium">
                  ¥15,680
                </div>
                <div class="text-sm text-gray-500">
                  本月收入
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between border border-gray-100 rounded-lg p-4">
              <div class="flex items-center gap-4">
                <div class="h-8 w-8 flex items-center justify-center rounded-full bg-gray-300 text-white">
                  2
                </div>
                <div class="flex-1">
                  <div class="font-medium">
                    刘志强
                  </div>
                  <div class="text-sm text-gray-500">
                    完成订单: 116
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-blue-500 font-medium">
                  ¥14,230
                </div>
                <div class="text-sm text-gray-500">
                  本月收入
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between border border-gray-100 rounded-lg p-4">
              <div class="flex items-center gap-4">
                <div class="h-8 w-8 flex items-center justify-center rounded-full bg-orange-400 text-white">
                  3
                </div>
                <div class="flex-1">
                  <div class="font-medium">
                    陈思远
                  </div>
                  <div class="text-sm text-gray-500">
                    完成订单: 98
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-blue-500 font-medium">
                  ¥12,460
                </div>
                <div class="text-sm text-gray-500">
                  本月收入
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧功能区 -->
      <div class="w-80">
        <!-- 快捷操作 -->
        <div class="mb-6 rounded-lg bg-white p-6">
          <h2 class="mb-4 text-lg font-medium">
            快捷操作
          </h2>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col cursor-pointer items-center rounded-lg p-4 transition-colors hover:bg-gray-50">
              <div class="mb-2 h-12 w-12 rounded-full bg-cyan-100 p-3">
                <el-icon class="h-full w-full text-cyan-500">
                  <Document />
                </el-icon>
              </div>
              <span class="text-sm" @click="goToCreateOrder">代客下单</span>
            </div>
            <div class="flex flex-col cursor-pointer items-center rounded-lg p-4 transition-colors hover:bg-gray-50">
              <div class="mb-2 h-12 w-12 rounded-full bg-purple-100 p-3">
                <el-icon class="h-full w-full text-purple-500">
                  <Ticket />
                </el-icon>
              </div>
              <span class="text-sm">快捷验券</span>
            </div>
            <div class="flex flex-col cursor-pointer items-center rounded-lg p-4 transition-colors hover:bg-gray-50">
              <div class="mb-2 h-12 w-12 rounded-full bg-amber-100 p-3">
                <img
                  src="https://ai-public.mastergo.com/ai/img_res/25f69f01f63e082fa97dae9facad4f94.jpg"
                  class="h-full w-full object-contain" alt="美团"
                >
              </div>
              <span class="whitespace-nowrap text-sm">授权美团开店宝</span>
            </div>
            <div class="flex flex-col cursor-pointer items-center rounded-lg p-4 transition-colors hover:bg-gray-50">
              <div class="mb-2 h-12 w-12 rounded-full bg-black p-3">
                <img
                  src="https://ai-public.mastergo.com/ai/img_res/f53d6b7219644460458ba06ae74ea134.jpg"
                  class="h-full w-full object-contain" alt="抖音"
                >
              </div>
              <span class="text-sm">授权抖音领客</span>
            </div>
          </div>
        </div>
        <!-- 雇主端小程序 -->
        <div class="rounded-lg bg-white p-6">
          <h2 class="mb-4 text-lg font-medium">
            雇主端小程序
          </h2>
          <div class="flex flex-col items-center">
            <img
              src="https://ai-public.mastergo.com/ai/img_res/e0514f495525af6da824de0d48f0f1e0.jpg"
              class="mb-4 h-48 w-48" alt="小程序码"
            >
            <div class="flex gap-4">
              <el-link type="primary">
                下载二维码
              </el-link>
              <el-link type="primary">
                装修小程序
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-link {
  font-weight: normal;
}
</style>
