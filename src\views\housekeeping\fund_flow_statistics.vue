<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="mx-auto max-w-7xl">
      <!-- 顶部导航 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex items-center gap-2">
          <span class="text-gray-600">门店</span>
          <el-select v-model="selectedStore" class="w-64">
            <el-option
              v-for="store in storeList"
              :key="store.uuid"
              :value="store.uuid"
              :label="store.name"
            />
          </el-select>
        </div>
        <div class="flex items-center gap-3">
          <div class="flex gap-3">
            <el-button
              v-for="item in statTypeList"
              :key="item.value"
              :type="activeSubTab === item.value ? 'primary' : ''"
              @click="activeSubTab = item.value"
            >
              {{ item.label }}
            </el-button>
          </div>
        </div>
      </div>
      <!-- 筛选条件 -->
      <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <div class="mb-4 flex items-center gap-4">
          <span class="text-gray-600">日期</span>
          <el-button-group>
            <el-button
              :type="dateRange === 'unlimited' ? 'primary' : 'default'"
              @click="dateRange = 'unlimited'"
            >
              不限
            </el-button>
            <el-button :type="dateRange === 'today' ? 'primary' : 'default'" @click="dateRange = 'today'">
              今日
            </el-button>
            <el-button
              :type="dateRange === 'yesterday' ? 'primary' : 'default'"
              @click="dateRange = 'yesterday'"
            >
              昨日
            </el-button>
            <el-button
              :type="dateRange === 'thisMonth' ? 'primary' : 'default'"
              @click="dateRange = 'thisMonth'"
            >
              本月
            </el-button>
            <el-button
              :type="dateRange === 'lastMonth' ? 'primary' : 'default'"
              @click="dateRange = 'lastMonth'"
            >
              上月
            </el-button>
          </el-button-group>
          <el-date-picker
            v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" class="w-48"
            @change="handleDateRangeChange"
          />
        </div>

        <div v-if="showMoreFilters" class="mb-4 flex items-center gap-4">
          <div class="mb-4 flex items-center gap-4">
            <span class="text-gray-600">收支</span>
            <el-button-group>
              <el-button
                v-for="type in payTypeList"
                :key="type.value"
                :type="payType === type.value ? 'primary' : 'default'"
                @click="payType = type.value"
              >
                {{ type.label }}
              </el-button>
            </el-button-group>
          </div>

          <div class="mb-4 flex items-center gap-4">
            <span class="text-gray-600">账户</span>
            <el-select v-model="selectedAccount" placeholder="请选择账户" class="w-48">
              <el-option v-for="account in accountList" :key="account.value" :label="account.label" :value="account.value" />
            </el-select>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <el-button @click="showMoreFilters = !showMoreFilters">
            {{ showMoreFilters ? '收起筛选' : '更多筛选条件' }}
            <el-icon class="ml-1">
              <component :is="showMoreFilters ? ArrowUp : ArrowDown" />
            </el-icon>
          </el-button>
          <div class="flex gap-3">
            <el-button @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" @click="handleSearch">
              查询
            </el-button>
          </div>
        </div>
      </div>
      <!-- 统计信息 -->
      <div v-if="tableData.length > 0" class="mb-4 text-sm text-gray-600">
        <p>收支明细，当前第{{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, total) }}条，共计{{ total }}条</p>
        <p>记账流水收入 <span class="text-green-500">{{ totalIncome }}</span>元，支出 <span class="text-red-500">{{ totalExpense }}</span>元</p>
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" class="w-full">
        <template #empty>
          <div class="py-8 text-center text-gray-500">
            <p class="mb-2 text-lg">
              暂无数据
            </p>
            <p>请选择筛选条件并点击查询按钮</p>
          </div>
        </template>
        <el-table-column prop="create_time" label="时间" width="140">
          <template #default="scope">
            {{ formatTableDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="op_user_name" label="操作人" width="140" />
        <el-table-column prop="type_name" label="收支类型" width="140" />
        <el-table-column prop="op_sub_type" label="收支项目" width="140" />
        <el-table-column prop="op_money" label="金额" width="140">
          <template #default="scope">
            <span :class="scope.row.op_money.startsWith('+') ? 'text-green-500' : 'text-red-500'">
              {{ scope.row.op_money }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="account_type_name" label="记账账户" width="140" />
        <el-table-column prop="op_info" label="收款说明" min-width="250" />
      </el-table>

      <!-- 分页 -->
      <div v-if="tableData.length > 0" class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 使用模拟数据，实际项目中应该导入相应的API
import fundFlowApi from '@/api/modules/housekeeping/fund_flow_statistics'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'

// 基本参数
const selectedStore = ref('')
const activeSubTab = ref('flow')
const dateRange = ref<string | any[]>('unlimited')
const showMoreFilters = ref(false)
const statType = ref('') // 统计类型
const selectedUser = ref('') // 用户UUID

// 定义类型
interface StoreItem { uuid: string, name: string }
interface UserItem { uuid: string, name: string }
interface SelectOption { value: string, label: string }
interface FundFlowItem {
  uuid: string
  create_time: string
  op_user_name: string
  type_name: string
  op_sub_type: string
  op_money: string
  account_type_name: string
  op_info: string
  [key: string]: any // 其他可能的字段
}

// 表格数据
const tableData = ref<FundFlowItem[]>([])

// 加载状态
const loading = ref(false)

// 门店列表
const storeList = ref<StoreItem[]>([])

// 用户列表
const userList = ref<UserItem[]>([])

// 统计类型列表
const statTypeList = ref<SelectOption[]>([

])

// 收支类型列表
const payTypeList = ref<SelectOption[]>([

])

// 收支类型和项目
const payType = ref('')
const payItem = ref('')

// 账户列表
const accountList = ref<SelectOption[]>([])
const selectedAccount = ref('')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表格数据类型定义在tableData中使用

// 统计数据
const totalIncome = ref('0.00')
const totalExpense = ref('0')

// 重置数据函数
function resetData() {
  tableData.value = []
  total.value = 0
  totalIncome.value = '0.00'
  totalExpense.value = '0'
}

// 获取筛选条件数据
async function getFilterData() {
  try {
    // 调用API获取筛选条件数据
    const response = await fundFlowApi.getTradeStatBasic({})

    if (response?.data) {
      // 处理门店列表
      if (response.data.store && response.data.store.length > 0) {
        storeList.value = response.data.store.map((item: any) => ({
          uuid: item.store_uuid,
          name: item.name,
        }))

        // 设置默认选中的门店
        const selectedStoreItem = response.data.store.find((item: any) => item.is_selected === 1)
        if (selectedStoreItem) {
          selectedStore.value = selectedStoreItem.store_uuid
        }
        else if (storeList.value.length > 0) {
          selectedStore.value = storeList.value[0].uuid
        }
      }

      // 处理账户列表
      if (response.data.account_types_list && response.data.account_types_list.length > 0) {
        accountList.value = response.data.account_types_list.map((item: any) => ({
          value: item.id.toString(),
          label: item.name,
        }))

        // 设置默认选中的账户
        const selectedAccountItem = response.data.account_types_list.find((item: any) => item.is_selected === 1)
        if (selectedAccountItem) {
          selectedAccount.value = selectedAccountItem.id.toString()
        }
      }

      // 处理统计类型列表
      if (response.data.stat_type_list && response.data.stat_type_list.length > 0) {
        statTypeList.value = response.data.stat_type_list.map((item: any) => ({
          value: item.stat_type_id.toString(),
          label: item.stat_type_name,
        }))

        // 设置默认选中的统计类型
        const selectedStatTypeItem = response.data.stat_type_list.find((item: any) => item.is_selected === 1)
        if (selectedStatTypeItem) {
          activeSubTab.value = selectedStatTypeItem.stat_type_id.toString()
        }
      }

      // 处理收支类型列表
      if (response.data.income_pay_type_list && response.data.income_pay_type_list.length > 0) {
        payTypeList.value = response.data.income_pay_type_list.map((item: any) => ({
          value: item.type_id,
          label: item.type_name,
        }))

        // 设置默认选中的收支类型
        const selectedPayTypeItem = response.data.income_pay_type_list.find((item: any) => item.is_selected === 1)
        if (selectedPayTypeItem) {
          payType.value = selectedPayTypeItem.type_id
        }
      }

      // 处理用户列表
      if (response.data.user_list && response.data.user_list.length > 0) {
        userList.value = response.data.user_list.map((item: any) => ({
          uuid: item.id,
          name: item.name,
        }))
      }
    }
  }
  catch (error) {
    console.error('获取筛选条件数据失败:', error)
    // 设置默认值
    storeList.value = []
    accountList.value = []
    statTypeList.value = []
    payTypeList.value = []
    userList.value = []
  }
}

// 格式化表格日期
function formatTableDate(dateStr: string): string {
  if (!dateStr)
    return ''

  // 只保留日期和时间部分，不显示秒
  const parts = dateStr.split(' ')
  if (parts.length === 2) {
    const timeParts = parts[1].split(':')
    return `${parts[0]}\n${timeParts[0]}:${timeParts[1]}`
  }
  return dateStr
}
// 获取资金流水数据
async function getTradeStatData() {
  loading.value = true

  try {
    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      size: pageSize.value,
      store_uuid: selectedStore.value,
    }

    // 添加可选参数（只有有值时才添加）
    if (activeSubTab.value)
      params.stat_type = activeSubTab.value
    if (payType.value)
      params.type = payType.value
    if (selectedUser.value)
      params.op_user_uuid = selectedUser.value
    if (payItem.value)
      params.op_sub_type = payItem.value
    if (selectedAccount.value)
      params.account_type = selectedAccount.value

    // 添加时间范围参数
    if (typeof dateRange.value === 'object' && dateRange.value.length === 2) {
      params.start_date = formatDate(dateRange.value[0], false, true)
      params.end_date = formatDate(dateRange.value[1], true, true)
    }
    else if (typeof dateRange.value === 'string' && dateRange.value !== 'unlimited') {
      const { startTime, endTime } = getTimeRangeByFilter(dateRange.value)
      if (startTime)
        params.start_date = startTime
      if (endTime)
        params.end_date = endTime
    }

    // 获取资金流水统计数据
    const response = await fundFlowApi.getTradeStatList(params)
    // 处理API响应
    if (response?.data) {
      const { list = [], total: totalCount = 0, total_data } = response.data
      // 更新表格数据和总数
      tableData.value = list
      total.value = totalCount

      // 更新统计数据
      if (total_data) {
        totalIncome.value = total_data.income_total || '0.00'
        totalExpense.value = (total_data.expenses_total || 0).toString()
      }
    }
    else {
      throw new Error(response?.data?.msg || '获取资金流水数据失败')
    }
  }
  catch (error) {
    // 统一处理错误
    console.error('获取资金流水数据失败:', error)
    const errorMsg = error instanceof Error ? error.message : '获取资金流水数据失败'
    ElMessage.error(errorMsg)
    resetData()
  }
  finally {
    loading.value = false
  }
}

// 根据时间筛选器获取时间范围
function getTimeRangeByFilter(filter: string): { startTime: string, endTime: string } {
  // 默认返回空时间范围
  if (filter === 'unlimited') {
    return { startTime: '', endTime: '' }
  }

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  // 定义时间范围映射
  const timeRanges: Record<string, { start: Date, end: Date }> = {
    today: {
      start: today,
      end: today,
    },
    yesterday: {
      start: new Date(today.setDate(today.getDate() - 1)),
      end: new Date(today),
    },
    thisMonth: {
      start: new Date(now.getFullYear(), now.getMonth(), 1),
      end: now,
    },
    lastMonth: {
      start: new Date(now.getFullYear(), now.getMonth() - 1, 1),
      end: new Date(now.getFullYear(), now.getMonth(), 0),
    },
  }

  // 获取选定的时间范围
  const range = timeRanges[filter] || { start: new Date(0), end: new Date(0) }

  // 格式化并返回时间范围
  return {
    startTime: formatDate(range.start, false, true),
    endTime: formatDate(range.end, true, true),
  }
}

// 格式化日期
function formatDate(date: Date, isEndOfDay = false, dateOnly = false): string {
  if (!date)
    return ''

  const d = new Date(date)

  // 设置为当天的最后一刻 (23:59:59.999)
  if (isEndOfDay)
    d.setHours(23, 59, 59, 999)

  // 格式化年月日
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')

  // 如果只需要日期部分
  if (dateOnly)
    return `${year}-${month}-${day}`

  // 格式化时分秒
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 重置筛选条件
function handleReset() {
  // 重置所有筛选条件
  dateRange.value = 'unlimited'
  statType.value = ''
  selectedUser.value = ''
  payType.value = ''
  payItem.value = ''
  selectedAccount.value = ''

  // 重置后不自动查询，只清空表格数据
  tableData.value = []
  total.value = 0
  totalIncome.value = '0.00'
  totalExpense.value = '0'
}

// 分页处理
function handleSizeChange(val: number) {
  pageSize.value = val
  getTradeStatData()
}

function handleCurrentChange(val: number) {
  currentPage.value = val
  getTradeStatData()
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1 // 重置到第一页
  getTradeStatData()
}

// 监听筛选条件变化 - 不自动触发查询
watch([activeSubTab, selectedUser, payType, payItem, selectedAccount], () => {
  // 不自动触发查询
})

// 监听门店切换 - 不自动触发查询
watch(selectedStore, (_newValue) => {
  // 不自动触发查询
})

// 处理日期范围变化 - 不自动触发查询
function handleDateRangeChange(val: any) {
  // 简化条件判断：有效日期范围则使用，否则设为'unlimited'
  dateRange.value = (val && val.length === 2) ? val : 'unlimited'
  // 不自动触发查询
}

// 页面加载时只获取筛选条件数据，不自动发送筛选请求
onMounted(async () => {
  // 初始化表格数据为空
  tableData.value = []
  total.value = 0
  getTradeStatData()
  // 获取筛选条件数据
  await getFilterData()
})
</script>

<style scoped>
/* 表格样式 */
.el-table {
  --el-table-border-color: var(--el-border-color);
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
}

/* 使用项目主题色变量 */
:deep(.el-button--primary) {
  --el-button-bg-color: var(--el-color-primary);
  --el-button-border-color: var(--el-color-primary);
  --el-button-hover-bg-color: var(--el-color-primary-light-3);
  --el-button-hover-border-color: var(--el-color-primary-light-3);
}

/* 文本颜色 */
:deep(.text-green-500) {
  color: var(--el-color-success);
}

:deep(.text-red-500) {
  color: var(--el-color-danger);
}

:deep(.text-blue-500) {
  color: var(--el-color-primary);
}
</style>
