<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">历史保单</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4 mb-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">保单状态</span>
            <el-select v-model="selectedStatus" placeholder="选择状态" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="status in statusList"
                :key="status.id"
                :label="status.name"
                :value="status.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">保险公司</span>
            <el-select v-model="selectedCompany" placeholder="选择公司" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="company in companyList"
                :key="company.id"
                :label="company.name"
                :value="company.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">保险类型</span>
            <el-select v-model="selectedType" placeholder="选择类型" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="type in typeList"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">门店</span>
            <el-select v-model="selectedStore" placeholder="选择门店" class="w-[150px]">
              <el-option label="全部" value="all" />
              <el-option
                v-for="store in storeList"
                :key="store.uuid"
                :label="store.name"
                :value="store.uuid"
              />
            </el-select>
          </div>
        </div>

        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">投保时间</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-[360px]"
            />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">关键词</span>
            <el-input v-model="keyword" placeholder="保单号/被保人姓名/手机号" class="w-[250px]" />
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap" @click="exportData">
              导出
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" class="w-full">
        <el-table-column prop="policyNo" label="保单号" width="180" />
        <el-table-column prop="insuredName" label="被保人" width="120" />
        <el-table-column prop="insuredPhone" label="手机号" width="120" />
        <el-table-column prop="insuranceType" label="保险类型" width="120" />
        <el-table-column prop="insuranceCompany" label="保险公司" width="120" />
        <el-table-column prop="premium" label="保费" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.premium }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="insuredAmount" label="保额" width="100">
          <template #default="scope">
            <span>¥{{ scope.row.insuredAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="生效日期" width="120" />
        <el-table-column prop="endDate" label="到期日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="storeName" label="门店" width="120" />
        <el-table-column label="操作" fixed="right" width="120">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 保单详情对话框 -->
    <el-dialog v-model="dialogVisible" title="保单详情" width="800px">
      <div v-if="detailLoading" class="flex justify-center items-center py-20">
        <el-spinner size="large" />
      </div>
      <div v-else>
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保单号</span>
            <span class="font-medium">{{ policyDetail.policyNo }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">被保人</span>
            <span class="font-medium">{{ policyDetail.insuredName }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">手机号</span>
            <span class="font-medium">{{ policyDetail.insuredPhone }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">身份证号</span>
            <span class="font-medium">{{ policyDetail.insuredIdCard }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保险类型</span>
            <span class="font-medium">{{ policyDetail.insuranceType }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保险公司</span>
            <span class="font-medium">{{ policyDetail.insuranceCompany }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保费</span>
            <span class="font-medium">¥{{ policyDetail.premium }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">保额</span>
            <span class="font-medium">¥{{ policyDetail.insuredAmount }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">生效日期</span>
            <span class="font-medium">{{ policyDetail.startDate }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">到期日期</span>
            <span class="font-medium">{{ policyDetail.endDate }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">状态</span>
            <span class="font-medium">
              <el-tag :type="getStatusType(policyDetail.status)">
                {{ policyDetail.statusText }}
              </el-tag>
            </span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm">门店</span>
            <span class="font-medium">{{ policyDetail.storeName }}</span>
          </div>
        </div>

        <div class="mb-6">
          <span class="text-gray-500 text-sm">保单说明</span>
          <div class="mt-1 p-3 border rounded-lg">
            {{ policyDetail.description || '无' }}
          </div>
        </div>

        <div class="mb-6">
          <span class="text-gray-500 text-sm">备注</span>
          <div class="mt-1 p-3 border rounded-lg">
            {{ policyDetail.remark || '无' }}
          </div>
        </div>

        <div v-if="policyDetail.attachments && policyDetail.attachments.length > 0">
          <span class="text-gray-500 text-sm">附件</span>
          <div class="mt-1 grid grid-cols-4 gap-4">
            <div v-for="(attachment, index) in policyDetail.attachments" :key="index" class="border rounded-lg p-2">
              <div class="flex flex-col items-center">
                <img :src="attachment.url" class="w-full h-24 object-cover mb-2" />
                <el-button type="primary" link @click="downloadAttachment(attachment)">下载</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import historyPolicyApi from '@/api/modules/housekeeping/history_policy';

// 筛选条件
const selectedStatus = ref('all');
const selectedCompany = ref('all');
const selectedType = ref('all');
const selectedStore = ref('all');
const dateRange = ref([]);
const keyword = ref('');

// 加载状态
const loading = ref(false);
const detailLoading = ref(false);

// 列表数据
const statusList = ref([]);
const companyList = ref([]);
const typeList = ref([]);
const storeList = ref([]);
const tableData = ref([]);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 详情对话框
const dialogVisible = ref(false);
const policyDetail = ref({});

// 获取保单状态列表
async function getPolicyStatusList() {
  try {
    const response = await historyPolicyApi.getPolicyStatusList();

    if (response.code === 200 && response.data) {
      statusList.value = response.data.list || [];
    } else {
      statusList.value = [];
    }
  } catch (error) {
    console.error('获取保单状态列表失败:', error);
    statusList.value = [];
  }
}

// 获取保险公司列表
async function getInsuranceCompanyList() {
  try {
    const response = await historyPolicyApi.getInsuranceCompanyList();

    if (response.code === 200 && response.data) {
      companyList.value = response.data.list || [];
    } else {
      companyList.value = [];
    }
  } catch (error) {
    console.error('获取保险公司列表失败:', error);
    companyList.value = [];
  }
}

// 获取保险类型列表
async function getInsuranceTypeList() {
  try {
    const response = await historyPolicyApi.getInsuranceTypeList();

    if (response.code === 200 && response.data) {
      typeList.value = response.data.list || [];
    } else {
      typeList.value = [];
    }
  } catch (error) {
    console.error('获取保险类型列表失败:', error);
    typeList.value = [];
  }
}

// 获取门店列表
async function getStoreList() {
  try {
    const response = await historyPolicyApi.getStoreList();

    if (response.code === 200 && response.data) {
      storeList.value = response.data.list || [];
    } else {
      storeList.value = [];
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    storeList.value = [];
  }
}

// 获取保单列表
async function getPolicyList() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: keyword.value || undefined,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value,
      company_id: selectedCompany.value === 'all' ? undefined : selectedCompany.value,
      type_id: selectedType.value === 'all' ? undefined : selectedType.value,
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await historyPolicyApi.getPolicyList(params);

    if (response.code === 200 && response.data) {
      tableData.value = response.data.list || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || '获取保单列表失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取保单列表失败:', error);
    ElMessage.error('获取保单列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 获取保单详情
async function getPolicyDetail(policyId) {
  try {
    detailLoading.value = true;

    const response = await historyPolicyApi.getPolicyDetail(policyId);

    if (response.code === 200 && response.data) {
      policyDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || '获取保单详情失败');
      policyDetail.value = {};
    }
  } catch (error) {
    console.error('获取保单详情失败:', error);
    ElMessage.error('获取保单详情失败');
    policyDetail.value = {};
  } finally {
    detailLoading.value = false;
  }
}

// 查看保单详情
function viewDetail(row) {
  dialogVisible.value = true;
  getPolicyDetail(row.id);
}

// 下载附件
function downloadAttachment(attachment) {
  if (attachment && attachment.url) {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || `attachment_${new Date().getTime()}`;
    link.target = '_blank';
    link.click();
  }
}

// 获取状态标签类型
function getStatusType(status) {
  switch (status) {
    case 'active':
      return 'success';
    case 'expired':
      return 'danger';
    case 'pending':
      return 'warning';
    case 'cancelled':
      return 'info';
    default:
      return 'info';
  }
}

// 格式化日期
function formatDate(date, isEndOfDay = false) {
  if (!date) return '';

  const d = new Date(date);

  if (isEndOfDay) {
    // 设置为当天的最后一刻 (23:59:59.999)
    d.setHours(23, 59, 59, 999);
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 导出保单数据
async function exportData() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      keyword: keyword.value || undefined,
      status: selectedStatus.value === 'all' ? undefined : selectedStatus.value,
      company_id: selectedCompany.value === 'all' ? undefined : selectedCompany.value,
      type_id: selectedType.value === 'all' ? undefined : selectedType.value,
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await historyPolicyApi.exportPolicyData(params);

    // 创建一个下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `保单数据_${new Date().getTime()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出保单数据失败:', error);
    ElMessage.error('导出保单数据失败');
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function handleReset() {
  selectedStatus.value = 'all';
  selectedCompany.value = 'all';
  selectedType.value = 'all';
  selectedStore.value = 'all';
  dateRange.value = [];
  keyword.value = '';

  handleSearch();
}

// 查询数据
function handleSearch() {
  currentPage.value = 1;
  getPolicyList();
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
  getPolicyList();
}

function handleCurrentChange(val) {
  currentPage.value = val;
  getPolicyList();
}

// 页面加载时获取数据
onMounted(() => {
  Promise.all([
    getPolicyStatusList(),
    getInsuranceCompanyList(),
    getInsuranceTypeList(),
    getStoreList()
  ]).then(() => {
    getPolicyList();
  });
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
