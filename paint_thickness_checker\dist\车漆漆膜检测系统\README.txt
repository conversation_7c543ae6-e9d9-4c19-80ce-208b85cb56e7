# 车漆漆膜检测系统 使用说明

## 系统简介
车漆漆膜检测系统是一款基于人工智能的图像识别软件，能够自动识别车漆厚度检测仪显示的数值，并进行智能分析判断。

## 系统要求
- Windows 10/11 (64位)
- 至少 4GB 内存
- 至少 3GB 可用磁盘空间
- 网络连接（首次运行时下载OCR模型）

## 安装说明
1. 将整个文件夹复制到任意目录
2. 双击运行 "车漆漆膜检测系统.exe"
3. 首次运行时会自动下载OCR模型，请保持网络连接
4. 下载过程可能需要几分钟，请耐心等待

## 使用方法

### 1. 启动程序
- 双击 "车漆漆膜检测系统.exe" 启动程序
- 程序启动后会自动在默认浏览器中打开系统页面
- 如果浏览器未自动打开，请手动访问 http://localhost:5000

### 2. 进行检测
1. 选择小组编号（第1组-第10组）
2. 选择车辆检测部位：
   - 前保险杠
   - 后保险杠
   - 前翼子板
   - 后翼子板
   - 车门
   - 引擎盖
   - 车顶
   - 后备箱盖

3. 上传检测图片：
   - 点击上传区域或拖拽图片文件
   - 支持格式：JPG、PNG、GIF、BMP
   - 文件大小限制：最大16MB
   - 确保图片中的数字清晰可见

4. 点击"开始检测"按钮进行分析

### 3. 查看结果
系统会自动识别图片中的厚度数值，并显示：
- 平均厚度
- 最大厚度
- 最小厚度
- 厚度差异
- 标准范围对比
- 检测点数量
- 异常问题分析

### 4. 生成报告
- 点击"生成检测报告"按钮
- 系统会生成详细的PDF格式报告
- 报告包含检测数据、图表分析和结论建议

## 检测标准

### 各部位标准厚度范围（微米）
- 前保险杠：80-150μm
- 后保险杠：80-150μm
- 前翼子板：100-180μm
- 后翼子板：100-180μm
- 车门：100-180μm
- 引擎盖：100-180μm
- 车顶：100-180μm
- 后备箱盖：100-180μm

### 判断标准
- 正常：厚度在标准范围内，差异小于50μm
- 厚度偏薄：平均厚度低于标准下限
- 厚度偏厚：平均厚度高于标准上限
- 厚度不均：最大最小厚度差异超过50μm

## 注意事项

### 图片要求
1. 图片清晰度要求高，数字必须清晰可见
2. 避免反光、阴影等影响识别的因素
3. 建议使用手机或相机拍摄，避免截图
4. 确保检测仪显示屏完整在画面中

### 使用建议
1. 每个部位建议拍摄多张照片进行对比
2. 如果识别结果异常，请检查图片质量
3. 系统识别结果仅供参考，请结合实际情况判断
4. 重要检测建议人工复核确认

## 故障排除

### 程序无法启动
1. 检查是否被杀毒软件拦截，添加信任
2. 确认系统版本为Windows 10/11 64位
3. 检查是否有足够的磁盘空间
4. 尝试以管理员身份运行

### OCR识别失败
1. 检查网络连接是否正常
2. 确认防火墙未阻止程序联网
3. 重新启动程序重试
4. 检查图片质量是否符合要求

### 浏览器无法访问
1. 检查防火墙设置，允许程序通过
2. 尝试手动访问 http://localhost:5000
3. 更换浏览器重试
4. 重新启动程序

### 识别结果不准确
1. 检查图片是否清晰
2. 确认数字显示完整
3. 避免图片中有干扰元素
4. 重新拍摄更清晰的照片

## 技术支持
如遇到其他问题，请联系技术支持团队：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

## 版本信息
- 版本：v1.0.0
- 发布日期：2025年
- 开发团队：车漆检测系统开发组

## 免责声明
本软件仅供辅助检测使用，检测结果仅供参考。实际车辆状况判断请结合专业人员意见和现场检查结果。开发团队不承担因使用本软件而产生的任何直接或间接损失。
