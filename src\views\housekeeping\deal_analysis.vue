<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">交易分析</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">时间范围</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-[360px]"
            />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">门店</span>
            <el-select v-model="selectedStore" placeholder="选择门店" class="w-[200px]">
              <el-option label="全部门店" value="all" />
              <el-option
                v-for="store in storeList"
                :key="store.uuid"
                :label="store.name"
                :value="store.uuid"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据概览卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div v-for="(card, index) in overviewCards" :key="index" class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-gray-500 text-sm mb-1">{{ card.title }}</div>
              <div class="text-2xl font-bold">{{ card.value }}</div>
            </div>
            <div :class="`text-${card.color}-500 text-lg`">
              <span>{{ card.change >= 0 ? '+' : '' }}{{ card.change }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 交易趋势 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h2 class="text-lg font-semibold mb-4">交易趋势</h2>
        <div class="h-80" ref="trendChartRef"></div>
      </div>

      <!-- 交易类型分布和支付方式分布 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">交易类型分布</h2>
          <div class="h-80" ref="typeChartRef"></div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">支付方式分布</h2>
          <div class="h-80" ref="paymentChartRef"></div>
        </div>
      </div>

      <!-- 热门服务项目和交易时段分布 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">热门服务项目</h2>
          <div class="h-80" ref="hotItemsChartRef"></div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">交易时段分布</h2>
          <div class="h-80" ref="timeDistChartRef"></div>
        </div>
      </div>

      <!-- 门店交易对比 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h2 class="text-lg font-semibold mb-4">门店交易对比</h2>
        <div class="h-80" ref="storeCompChartRef"></div>
      </div>

      <!-- 导出按钮 -->
      <div class="flex justify-end mb-6">
        <el-button type="primary" @click="exportData">导出分析数据</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import dealAnalysisApi from '@/api/modules/housekeeping/deal_analysis';
import * as echarts from 'echarts';

// 筛选条件
const dateRange = ref([]);
const selectedStore = ref('all');

// 加载状态
const loading = ref(false);

// 门店列表
const storeList = ref([]);

// 概览数据
const overviewCards = ref([
  { title: '总交易金额', value: '¥0', change: 0, color: 'blue' },
  { title: '总交易笔数', value: '0', change: 0, color: 'green' },
  { title: '平均单价', value: '¥0', change: 0, color: 'orange' },
  { title: '订单转化率', value: '0%', change: 0, color: 'purple' },
]);

// 图表引用
const trendChartRef = ref(null);
const typeChartRef = ref(null);
const paymentChartRef = ref(null);
const hotItemsChartRef = ref(null);
const timeDistChartRef = ref(null);
const storeCompChartRef = ref(null);

// 图表实例
let trendChart = null;
let typeChart = null;
let paymentChart = null;
let hotItemsChart = null;
let timeDistChart = null;
let storeCompChart = null;

// 获取门店列表
async function getStoreList() {
  try {
    const response = await dealAnalysisApi.getStoreList();

    if (response.code === 200 && response.data) {
      storeList.value = response.data.list || [];
    } else {
      storeList.value = [];
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    storeList.value = [];
  }
}

// 获取交易分析概览数据
async function getDealAnalysisOverview() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getDealAnalysisOverview(params);

    if (response.code === 200 && response.data) {
      // 更新概览数据
      overviewCards.value[0].value = `¥${response.data.total_amount || '0'}`;
      overviewCards.value[0].change = response.data.total_amount_change || 0;

      overviewCards.value[1].value = response.data.total_count || '0';
      overviewCards.value[1].change = response.data.total_count_change || 0;

      overviewCards.value[2].value = `¥${response.data.average_price || '0'}`;
      overviewCards.value[2].change = response.data.average_price_change || 0;

      overviewCards.value[3].value = `${response.data.conversion_rate || '0'}%`;
      overviewCards.value[3].change = response.data.conversion_rate_change || 0;
    }
  } catch (error) {
    console.error('获取交易分析概览数据失败:', error);
  }
}

// 获取交易趋势数据
async function getDealTrend() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getDealTrend(params);

    if (response.code === 200 && response.data) {
      // 初始化交易趋势图表
      initTrendChart(response.data);
    }
  } catch (error) {
    console.error('获取交易趋势数据失败:', error);
  }
}

// 获取交易类型分布数据
async function getDealTypeDistribution() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getDealTypeDistribution(params);

    if (response.code === 200 && response.data) {
      // 初始化交易类型分布图表
      initTypeChart(response.data);
    }
  } catch (error) {
    console.error('获取交易类型分布数据失败:', error);
  }
}

// 获取支付方式分布数据
async function getPaymentMethodDistribution() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getPaymentMethodDistribution(params);

    if (response.code === 200 && response.data) {
      // 初始化支付方式分布图表
      initPaymentChart(response.data);
    }
  } catch (error) {
    console.error('获取支付方式分布数据失败:', error);
  }
}

// 获取热门服务项目数据
async function getHotServiceItems() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getHotServiceItems(params);

    if (response.code === 200 && response.data) {
      // 初始化热门服务项目图表
      initHotItemsChart(response.data);
    }
  } catch (error) {
    console.error('获取热门服务项目数据失败:', error);
  }
}

// 获取交易时段分布数据
async function getDealTimeDistribution() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getDealTimeDistribution(params);

    if (response.code === 200 && response.data) {
      // 初始化交易时段分布图表
      initTimeDistChart(response.data);
    }
  } catch (error) {
    console.error('获取交易时段分布数据失败:', error);
  }
}

// 获取门店交易对比数据
async function getStoreComparison() {
  try {
    // 构建查询参数
    const params = {};

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.getStoreComparison(params);

    if (response.code === 200 && response.data) {
      // 初始化门店交易对比图表
      initStoreCompChart(response.data);
    }
  } catch (error) {
    console.error('获取门店交易对比数据失败:', error);
  }
}

// 格式化日期
function formatDate(date, isEndOfDay = false) {
  if (!date) return '';

  const d = new Date(date);

  if (isEndOfDay) {
    // 设置为当天的最后一刻 (23:59:59.999)
    d.setHours(23, 59, 59, 999);
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 初始化交易趋势图表
function initTrendChart(data) {
  if (!trendChartRef.value) return;

  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['交易金额', '交易笔数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dates || []
    },
    yAxis: [
      {
        type: 'value',
        name: '交易金额',
        position: 'left'
      },
      {
        type: 'value',
        name: '交易笔数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '交易金额',
        type: 'bar',
        data: data.amounts || []
      },
      {
        name: '交易笔数',
        type: 'line',
        yAxisIndex: 1,
        data: data.counts || []
      }
    ]
  };

  trendChart.setOption(option);
}

// 初始化交易类型分布图表
function initTypeChart(data) {
  if (!typeChartRef.value) return;

  if (!typeChart) {
    typeChart = echarts.init(typeChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.types?.map(item => item.name) || []
    },
    series: [
      {
        name: '交易类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.types?.map(item => ({
          value: item.value,
          name: item.name
        })) || []
      }
    ]
  };

  typeChart.setOption(option);
}

// 初始化支付方式分布图表
function initPaymentChart(data) {
  if (!paymentChartRef.value) return;

  if (!paymentChart) {
    paymentChart = echarts.init(paymentChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.payment_methods?.map(item => item.name) || []
    },
    series: [
      {
        name: '支付方式',
        type: 'pie',
        radius: '55%',
        center: ['50%', '60%'],
        data: data.payment_methods?.map(item => ({
          value: item.value,
          name: item.name
        })) || [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  paymentChart.setOption(option);
}

// 初始化热门服务项目图表
function initHotItemsChart(data) {
  if (!hotItemsChartRef.value) return;

  if (!hotItemsChart) {
    hotItemsChart = echarts.init(hotItemsChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data.items?.map(item => item.name) || [],
      inverse: true
    },
    series: [
      {
        name: '销售金额',
        type: 'bar',
        data: data.items?.map(item => item.amount) || []
      }
    ]
  };

  hotItemsChart.setOption(option);
}

// 初始化交易时段分布图表
function initTimeDistChart(data) {
  if (!timeDistChartRef.value) return;

  if (!timeDistChart) {
    timeDistChart = echarts.init(timeDistChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.hours || []
    },
    yAxis: [
      {
        type: 'value',
        name: '交易笔数'
      }
    ],
    series: [
      {
        name: '交易笔数',
        type: 'bar',
        data: data.counts || []
      }
    ]
  };

  timeDistChart.setOption(option);
}

// 初始化门店交易对比图表
function initStoreCompChart(data) {
  if (!storeCompChartRef.value) return;

  if (!storeCompChart) {
    storeCompChart = echarts.init(storeCompChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['交易金额', '交易笔数', '平均单价']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.stores?.map(item => item.name) || []
    },
    yAxis: [
      {
        type: 'value',
        name: '交易金额',
        position: 'left'
      },
      {
        type: 'value',
        name: '交易笔数/平均单价',
        position: 'right'
      }
    ],
    series: [
      {
        name: '交易金额',
        type: 'bar',
        data: data.stores?.map(item => item.amount) || []
      },
      {
        name: '交易笔数',
        type: 'bar',
        yAxisIndex: 1,
        data: data.stores?.map(item => item.count) || []
      },
      {
        name: '平均单价',
        type: 'line',
        yAxisIndex: 1,
        data: data.stores?.map(item => item.average_price) || []
      }
    ]
  };

  storeCompChart.setOption(option);
}

// 导出分析数据
async function exportData() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await dealAnalysisApi.exportDealAnalysisData(params);

    // 创建一个下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `交易分析数据_${new Date().getTime()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出分析数据失败:', error);
    ElMessage.error('导出分析数据失败');
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function handleReset() {
  dateRange.value = [];
  selectedStore.value = 'all';
  handleSearch();
}

// 查询数据
async function handleSearch() {
  try {
    loading.value = true;

    // 获取所有分析数据
    await Promise.all([
      getDealAnalysisOverview(),
      getDealTrend(),
      getDealTypeDistribution(),
      getPaymentMethodDistribution(),
      getHotServiceItems(),
      getDealTimeDistribution(),
      getStoreComparison()
    ]);
  } catch (error) {
    console.error('查询数据失败:', error);
    ElMessage.error('查询数据失败');
  } finally {
    loading.value = false;
  }
}

// 页面加载时获取数据
onMounted(() => {
  getStoreList();
  handleSearch();
});

// 页面卸载时释放图表实例
onUnmounted(() => {
  if (trendChart) trendChart.dispose();
  if (typeChart) typeChart.dispose();
  if (paymentChart) paymentChart.dispose();
  if (hotItemsChart) hotItemsChart.dispose();
  if (timeDistChart) timeDistChart.dispose();
  if (storeCompChart) storeCompChart.dispose();
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
