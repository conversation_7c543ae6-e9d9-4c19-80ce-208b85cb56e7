<template>
  <div class="company-settings">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="系统模块显示设置" name="system">
        <div class="settings-section">
          <h3 class="section-title">系统模块显示设置</h3>
          <el-form class="system-settings-form">
            <el-form-item label="服务人员模块">
              <el-switch v-model="systemSettings.moduleEnabled" active-color="#13ce66" />
            </el-form-item>
            <el-form-item label="APP端是否可以查看到家板块">
              <el-switch v-model="systemSettings.appDisplayEnabled" active-color="#13ce66" />
            </el-form-item>
            <el-button type="primary" @click="saveSystemSettings" class="save-button">
              保存
            </el-button>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="到家通知设置" name="notification">
        <div class="settings-section">
          <!-- 客户通知设置 -->
          <div class="notification-section">
            <h3 class="section-title">
              客户通知设置
              <el-link type="primary" class="view-all-link">查看所有客户发送的全部通知</el-link>
            </h3>
            <el-form class="notification-form">
              <el-form-item label="商家后台代客下单扣除余额">
                <el-select v-model="notificationSettings.customer[0].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="订单派单时通知客户">
                <el-select v-model="notificationSettings.customer[1].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="创建套餐使用服务后通知客户">
                <el-select v-model="notificationSettings.customer[2].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="商家后台取消服务单">
                <el-select v-model="notificationSettings.customer[3].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="服务开始前1天提醒客户">
                <el-select v-model="notificationSettings.customer[4].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="服务完成后邀请客户评价">
                <el-select v-model="notificationSettings.customer[5].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="商家后台充值扣除余额成功提醒" class="keyboard-shortcut-item">
                <div class="keyboard-shortcut">按住 Shift + Alt + A</div>
                <el-select v-model="notificationSettings.customer[6].type">
                  <el-option label="短信通知" value="sms" />
                  <el-option label="不通知" value="none" />
                </el-select>
              </el-form-item>

              <el-form-item label="余额不足" class="balance-threshold-item">
                <el-input-number v-model="balanceThreshold" :min="0" :precision="0" class="threshold-input" />
                <span class="threshold-text">元时提醒一次客户充值（余额小于等于0元），通知方式</span>
                <el-select v-model="balanceNotificationType" class="threshold-select">
                  <el-option label="不通知" value="none" />
                  <el-option label="短信通知" value="sms" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <!-- 服务人员通知设置 -->
          <div class="notification-section">
            <h3 class="section-title">
              服务人员通知设置
              <el-link type="primary" class="view-all-link">查看所有服务人员发送的全部通知内容</el-link>
            </h3>
            <el-form class="notification-form">
              <el-form-item label="订单派单时通知服务人员">
                <el-select v-model="notificationSettings.staff[0].type">
                  <el-option label="微信和短信通知" value="wechat_sms" />
                  <el-option label="微信通知" value="wechat" />
                </el-select>
              </el-form-item>

              <el-form-item label="订单服务信息修改通知">
                <el-select v-model="notificationSettings.staff[1].type">
                  <el-option label="微信和短信通知" value="wechat_sms" />
                  <el-option label="微信通知" value="wechat" />
                </el-select>
              </el-form-item>

              <el-form-item label="订单取消或将服务人员从订单移除通知">
                <el-select v-model="notificationSettings.staff[2].type">
                  <el-option label="微信和短信通知" value="wechat_sms" />
                  <el-option label="微信通知" value="wechat" />
                </el-select>
              </el-form-item>

              <el-form-item label="发布抢单时通知服务人员抢单">
                <el-select v-model="notificationSettings.staff[3].type">
                  <el-option label="微信通知" value="wechat" />
                  <el-option label="微信和短信通知" value="wechat_sms" />
                </el-select>
              </el-form-item>

              <el-form-item label="派单后服务人员未接单提醒" class="staff-reminder-item">
                <span class="reminder-label">服务开始前</span>
                <el-input-number v-model="staffReminderHours" :min="0" :precision="0" class="reminder-input" />
                <span class="reminder-text">小时提醒服务人员接单，通知方式</span>
                <el-select v-model="staffReminderType" class="reminder-select">
                  <el-option label="0" value="0" />
                </el-select>
                <div class="reminder-note">若设置0小时，系统会在服务的开始时间发送通知</div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 商家通知设置 -->
          <div class="notification-section">
            <h3 class="section-title">
              商家通知设置
              <el-link type="primary" class="view-all-link">查看所有商家发送的全部通知内容</el-link>
              <span class="merchant-note">若短信中存在敏感词则系统会自动替换为“*”，点击查看敏感词</span>
            </h3>
            <el-form class="notification-form">
              <el-form-item label="新订单通知">
                <el-select v-model="notificationSettings.merchant[0].type">
                  <el-option label="短信和系统通知" value="sms_system" />
                </el-select>
              </el-form-item>

              <el-form-item label="订单取消通知（非商家手动取消）">
                <el-select v-model="notificationSettings.merchant[1].type">
                  <el-option label="短信和系统通知" value="sms_system" />
                </el-select>
              </el-form-item>

              <el-form-item label="订单修改通知（非商家手动修改）">
                <el-select v-model="notificationSettings.merchant[2].type">
                  <el-option label="短信和系统通知" value="sms_system" />
                </el-select>
              </el-form-item>

              <el-form-item label="美团客户提交预约改期申请通知">
                <el-select v-model="notificationSettings.merchant[3].type">
                  <el-option label="系统通知" value="system" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <el-button type="primary" @click="saveNotificationSettings" class="save-button">
            保存
          </el-button>
        </div>
      </el-tab-pane>

      <el-tab-pane label="服务人员接单设置" name="staff">
        <div class="settings-section">
          <h3 class="section-title">服务人员接单设置</h3>
          <el-form class="staff-settings-form">
            <el-form-item label="服务人员打卡（签到签退）规则设置" class="help-icon-item">
              <el-select v-model="staffSettings.checkInRule">
                <el-option label="拒照打卡-距客户家500米外才能拍照打卡" value="distance_500m" />
              </el-select>
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-form-item>

            <el-form-item label="开始服务和结束服务的打卡时间间隔">
              <el-select v-model="staffSettings.checkInIntervalOption">
                <el-option label="0" value="0" />
              </el-select>
              <span class="unit">分钟</span>
            </el-form-item>

            <el-form-item label="发布抢单开关">
              <el-select v-model="staffSettings.orderGrabEnabled">
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>

            <el-form-item label="服务人员端服务前后照片上传开关">
              <el-select v-model="staffSettings.photoUploadEnabled">
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>

            <el-form-item label="服务人员拒绝接单">
              <el-select v-model="staffSettings.rejectOrderEnabled">
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>

            <el-form-item label="是否允许服务人员在小程序收款">
              <el-select v-model="staffSettings.paymentEnabled">
                <el-option label="是" value="yes" />
                <el-option label="否" value="no" />
              </el-select>
            </el-form-item>

            <el-button type="primary" @click="saveStaffSettings" class="save-button">
              保存
            </el-button>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="到家云系统设置" name="cloud">
        <div class="settings-section">
          <h3 class="section-title">到家云系统设置</h3>
          <el-form class="cloud-settings-form">
            <el-form-item label="代客下单储值余额支付">
              <el-select v-model="cloudSettings.balancePaymentEnabled">
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>

            <el-form-item label="隐藏雇主和服务人员之间的联系方式">
              <el-select v-model="cloudSettings.hideContactInfo">
                <el-option label="不隐藏" value="show" />
                <el-option label="隐藏" value="hide" />
              </el-select>
            </el-form-item>

            <el-form-item label="派单之间要求时间间隔" class="interval-item">
              <el-input-number v-model="cloudSettings.dispatchInterval" :min="0" :step="1" class="interval-input" />
              <span class="unit">分钟</span>
            </el-form-item>

            <el-form-item label="雇主录入时要求填写户型和面积">
              <el-select v-model="cloudSettings.requireHouseInfo">
                <el-option label="不要求" value="no" />
                <el-option label="要求" value="yes" />
              </el-select>
            </el-form-item>

            <el-form-item label="服务人员身份证号是否必填">
              <el-select v-model="cloudSettings.requireIdCard">
                <el-option label="必填" value="required" />
                <el-option label="选填" value="optional" />
              </el-select>
            </el-form-item>

            <el-form-item label="服务地址与门店城市不一致提示客户">
              <el-select v-model="cloudSettings.addressMismatchAlert">
                <el-option label="开启" value="enabled" />
                <el-option label="关闭" value="disabled" />
              </el-select>
            </el-form-item>

            <el-button type="primary" @click="saveCloudSettings" class="save-button">
              保存
            </el-button>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const activeTab = ref('system')

const systemSettings = reactive({
  moduleEnabled: true,
  appDisplayEnabled: true,
})

const notificationSettings = reactive({
  customer: [
    { label: '商家后台代客下单扣除余额', type: 'sms' },
    { label: '订单派单时通知客户', type: 'none' },
    { label: '创建套餐使用服务后通知客户', type: 'sms' },
    { label: '商家后台取消服务单', type: 'sms' },
    { label: '服务开始前1天提醒客户', type: 'sms' },
    { label: '服务完成后邀请客户评价', type: 'none' },
    { label: '商家后台充值扣除余额成功提醒', type: 'sms' },
  ],
  staff: [
    { label: '订单派单时通知服务人员', type: 'wechat_sms' },
    { label: '订单服务信息修改通知', type: 'wechat_sms' },
    { label: '订单取消或将服务人员从订单移除通知', type: 'wechat_sms' },
    { label: '发布抢单时通知服务人员抢单', type: 'wechat' },
  ],
  merchant: [
    { label: '新订单通知', type: 'sms_system' },
    { label: '订单取消通知（非商家手动取消）', type: 'sms_system' },
    { label: '订单修改通知（非商家手动修改）', type: 'sms_system' },
    { label: '美团客户提交预约改期申请通知', type: 'system' },
  ],
})

// 余额不足提醒相关设置
const balanceThreshold = ref(100)
const balanceNotificationType = ref('none')

// 服务人员未接单提醒相关设置
const staffReminderHours = ref(4)
const staffReminderType = ref('0')

const staffSettings = reactive({
  checkInRule: 'distance_500m',
  checkInInterval: 0,
  checkInIntervalOption: '0', // 新增下拉选择框选项
  orderGrabEnabled: 'enabled',
  photoUploadEnabled: 'enabled',
  rejectOrderEnabled: 'enabled',
  paymentEnabled: 'yes',
})

const cloudSettings = reactive({
  balancePaymentEnabled: 'enabled',
  hideContactInfo: 'show',
  dispatchInterval: 60,
  requireHouseInfo: 'no',
  requireIdCard: 'required',
  addressMismatchAlert: 'enabled',
})

function saveSystemSettings() {
  // 实现保存系统设置的逻辑
}

function saveNotificationSettings() {
  // 实现保存通知设置的逻辑
}

function saveStaffSettings() {
  // 实现保存服务人员设置的逻辑
}

function saveCloudSettings() {
  // 实现保存云系统设置的逻辑
}
</script>

<style scoped>
.company-settings {
  padding: 20px;
}

.settings-section {
  padding: 24px;
  margin: 20px 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.settings-section h3 {
  padding-bottom: 12px;
  margin: 0 0 24px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #ebeef5;
}

.notification-section {
  margin-bottom: 40px;
}

.notification-section:last-child {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  padding-bottom: 12px;
  margin: 0 0 24px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #ebeef5;
}

.view-all-link {
  font-size: 14px;
  font-weight: normal;
}

.merchant-note {
  font-size: 14px;
  font-weight: normal;
  color: #909399;
}

.notification-form {
  max-width: 800px;
}

.el-form {
  max-width: 800px;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item :deep(.el-form-item__label) {
  width: 240px;
  padding-right: 24px;
  text-align: right;
}

.el-select,
.el-input-number {
  width: 300px;
}

/* 特殊表单项样式 */
.keyboard-shortcut-item {
  position: relative;
}

.keyboard-shortcut {
  position: absolute;
  top: 0;
  right: 320px;
  padding: 2px 8px;
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.balance-threshold-item :deep(.el-form-item__content),
.staff-reminder-item :deep(.el-form-item__content) {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.threshold-input,
.reminder-input {
  width: 100px !important;
}

.threshold-text,
.reminder-text {
  color: #606266;
}

.threshold-select,
.reminder-select {
  width: 120px !important;
}

.reminder-note {
  width: 100%;
  margin-top: 8px;
  margin-left: 100px;
  font-size: 12px;
  color: #909399;
}

.reminder-label {
  color: #606266;
}

.unit {
  margin-left: 8px;
  color: #606266;
}

/* 帮助图标样式 */
.help-icon-item {
  position: relative;
}

.help-icon {
  position: absolute;
  top: 50%;
  margin-left: 8px;
  font-size: 16px;
  color: #909399;
  transform: translateY(-50%);
  cursor: help;
}

.staff-settings-form,
.cloud-settings-form,
.system-settings-form {
  max-width: 800px;
}

.interval-item :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

.interval-input {
  width: 100px !important;
}

.save-button {
  min-width: 120px;
  margin-top: 32px;
}

@media (width <= 768px) {
  .settings-section {
    padding: 16px;
  }

  .el-form-item :deep(.el-form-item__label) {
    width: 100%;
    padding-right: 0;
    margin-bottom: 8px;
    text-align: left;
  }

  .el-select,
  .el-input-number {
    width: 100%;
  }

  .keyboard-shortcut {
    position: static;
    display: block;
    margin-bottom: 8px;
  }

  .reminder-note {
    margin-left: 0;
  }
}

/* 表单校验提示样式 */
.el-form-item.is-error :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

.el-form-item__error {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-color-danger);
}
</style>
