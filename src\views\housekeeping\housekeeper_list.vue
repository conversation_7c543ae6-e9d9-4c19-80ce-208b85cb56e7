<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 py-6">
      <!-- 顶部按钮组 -->
      <div class="mb-6 flex gap-4">
        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showAddDialog = true">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>添加家政员简历
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap" @click="showImportDialog = true">
          <el-icon class="mr-1">
            <Upload />
          </el-icon>批量导入简历
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap" @click="$router.push('/housekeeping/import_records')">
          <el-icon class="mr-1">
            <Document />
          </el-icon>导入记录
        </el-button>
      </div>
      <!-- 批量导入简历弹窗 -->
      <el-dialog v-model="showImportDialog" title="批量导入简历数据" width="500px" destroy-on-close>
        <div class="p-6">
          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              上传文件 (格式xlsx, 2MB以内)
            </div>
            <el-upload
              class="upload-demo"
              action="/api/upload"
              :on-change="handleFileChange"
              :before-upload="beforeUpload"
              :auto-upload="false"
              accept=".xlsx"
            >
              <el-button type="primary" class="!rounded-button">
                <el-icon class="mr-1">
                  <Upload />
                </el-icon>选择文件
              </el-button>
            </el-upload>
          </div>

          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              设置数据归属账号 (数据导入后，归属到该账号名下)
            </div>
            <el-select v-model="importForm.account" placeholder="请选择" class="w-full">
              <el-option label="厦门小羽佳线索店" value="store1" />
            </el-select>
          </div>

          <div class="text-sm text-gray-500">
            <div class="mb-2">
              注:
            </div>
            <div class="mb-1">
              1.请按照导入模板，整理好数据再上传录入。点击<a href="#" class="text-primary-500">下载简历导入模板</a>
            </div>
            <div class="mb-1">
              2.数据表限制20000条以内，文件大小限制2MB
            </div>
            <div>3.简历数据中，手机号或身份证号至少存在一个，已存在的数据（身份证一致），将不会导入。</div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-4">
            <el-button @click="showImportDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="handleImport">
              确定录入
            </el-button>
          </div>
        </template>
      </el-dialog>

      <el-dialog v-model="showAddDialog" title="添加家政员简历" width="800px" destroy-on-close>
        <div class="p-6">
          <div class="mb-6">
            <div class="mb-2 text-lg font-medium">
              身份信息
            </div>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
              <el-form-item label="身份证照片" prop="idCardPhotos">
                <el-upload
                  class="avatar-uploader"
                  action="/api/upload"
                  :show-file-list="false"
                  :on-success="handleIdCardUploadSuccess"
                >
                  <div class="hover:border-primary-500 h-40 w-64 flex cursor-pointer items-center justify-center border-2 border-gray-300 border-dashed bg-gray-50">
                    <el-icon v-if="!form.idCardPhotos" class="text-3xl text-gray-400">
                      <Plus />
                    </el-icon>
                    <img v-else :src="form.idCardPhotos" class="h-full w-full object-cover">
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="身份证号" prop="idCardNo">
                <el-input v-model="form.idCardNo" placeholder="请输入身份证号" />
              </el-form-item>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="生日" prop="birthday">
                <el-date-picker v-model="form.birthday" type="date" placeholder="请选择生日" />
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="form.gender">
                  <el-radio label="male">
                    男
                  </el-radio>
                  <el-radio label="female">
                    女
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="民族" prop="ethnicity">
                <el-select v-model="form.ethnicity" placeholder="请选择民族">
                  <el-option label="汉族" value="han" />
                  <el-option label="其他民族" value="other" />
                </el-select>
              </el-form-item>
              <el-form-item label="籍贯" prop="nativePlace">
                <el-cascader v-model="form.nativePlace" :options="provinceOptions" placeholder="请选择籍贯" />
              </el-form-item>
              <el-form-item label="电话号码" prop="tel">
                <el-input v-model="form.tel" placeholder="请输入电话号码" />
              </el-form-item>
              <el-form-item label="身份证地址" prop="idAddress">
                <el-input v-model="form.idAddress" placeholder="请输入身份证地址" />
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="form.agreement">
                  我已阅读并同意《个人信息收集使用协议》
                </el-checkbox>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-4">
            <el-button @click="showAddDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="handleSubmit">
              保存
            </el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="showShareDialog" title="分享家政员" width="400px" destroy-on-close>
        <div class="p-6">
          <div class="mb-6 flex items-center gap-4">
            <div class="h-16 w-16 overflow-hidden rounded-full">
              <img :src="currentHousekeeper?.avatar" class="h-full w-full object-cover" @error="$event.target.src = '/public/LOGO金刚(圆).png'">
            </div>
            <div>
              <div class="text-lg font-medium">
                {{ currentHousekeeper?.name }}
              </div>
              <div class="text-sm text-gray-500">
                {{ currentHousekeeper?.age }}岁 | {{ currentHousekeeper?.experience }}年经验
              </div>
            </div>
          </div>

          <div class="mb-4">
            <div class="mb-2 text-gray-600">
              技能特长
            </div>
            <div class="flex flex-wrap gap-2">
              <el-tag v-for="(skill, index) in ['做饭', '照顾老人', '照顾小孩', '做家务']" :key="index" size="small">
                {{ skill }}
              </el-tag>
            </div>
          </div>

          <div class="mb-4">
            <div class="mb-2 text-gray-600">
              证书资质
            </div>
            <div class="flex flex-wrap gap-2">
              <el-tag type="success" size="small">
                健康证
              </el-tag>
              <el-tag type="success" size="small">
                月嫂证
              </el-tag>
            </div>
          </div>

          <div class="mb-4">
            <div class="mb-2 text-gray-600">
              分享链接
            </div>
            <el-input v-model="shareLink" readonly>
              <template #append>
                <el-button @click="copyShareLink">
                  复制
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </el-dialog>
      <el-dialog v-model="showFollowupDialog" title="添加跟进" width="500px" destroy-on-close>
        <div class="p-6">
          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              填写家政员的沟通情况、面试情况或近况等内容。
            </div>
            <el-input
              v-model="followupForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入跟进内容"
              show-word-limit
              maxlength="1000"
            />
          </div>

          <div class="mb-6">
            <el-upload
              class="upload-demo"
              action="/api/upload"
              :auto-upload="false"
              :on-change="handleFollowupImageChange"
            >
              <div class="hover:border-primary-500 h-40 w-64 flex cursor-pointer items-center justify-center border-2 border-gray-300 border-dashed bg-gray-50">
                <el-icon class="text-3xl text-gray-400">
                  <Plus />
                </el-icon>
                <span class="ml-2">点击上传</span>
              </div>
            </el-upload>
          </div>

          <div class="mb-6">
            <el-checkbox v-model="followupForm.setReminder">
              同时设置为待办提醒
            </el-checkbox>
            <div v-show="followupForm.setReminder" class="mt-4">
              <div class="mb-4">
                <div class="mb-2 text-gray-600">
                  提醒类型
                </div>
                <el-radio-group v-model="followupForm.reminderType" class="flex flex-wrap gap-2">
                  <el-radio label="contract">
                    合同到期
                  </el-radio>
                  <el-radio label="insurance">
                    保险到期
                  </el-radio>
                  <el-radio label="interview">
                    面试提醒
                  </el-radio>
                  <el-radio label="followup">
                    回访提醒
                  </el-radio>
                  <el-radio label="other">
                    其他
                  </el-radio>
                </el-radio-group>
              </div>
              <div class="mb-4">
                <div class="mb-2 text-gray-600">
                  提醒时间
                </div>
                <el-date-picker v-model="followupForm.reminderTime" type="datetime" placeholder="选择提醒时间" class="w-full" />
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-4">
            <el-button @click="showFollowupDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="handleFollowupSubmit">
              确认
            </el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="showEvaluationDialog" title="添加评价" width="500px" destroy-on-close>
        <div class="p-6">
          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              评分
            </div>
            <el-rate v-model="evaluationForm.rating" />
          </div>

          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              评价内容
            </div>
            <el-input
              v-model="evaluationForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入评价内容"
              show-word-limit
              maxlength="1000"
            />
          </div>

          <div class="mb-6">
            <div class="mb-2 text-gray-600">
              上传图片
            </div>
            <el-upload
              class="upload-demo"
              action="/api/upload"
              :auto-upload="false"
              :on-change="handleEvaluationImageChange"
              multiple
            >
              <div class="hover:border-primary-500 h-40 w-64 flex cursor-pointer items-center justify-center border-2 border-gray-300 border-dashed bg-gray-50">
                <el-icon class="text-3xl text-gray-400">
                  <Plus />
                </el-icon>
                <span class="ml-2">点击上传</span>
              </div>
            </el-upload>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-4">
            <el-button @click="showEvaluationDialog = false">
              取消
            </el-button>
            <el-button type="primary" @click="handleEvaluationSubmit">
              确认
            </el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 家政员详细 -->
      <el-drawer
        v-model="drawerVisible" title="家政员详情" size="80%" direction="rtl" destroy-on-close
      >
        <template #header>
          <div class="flex items-center gap-4">
            <div v-if="currentHousekeeper" class="flex items-center gap-3">
              <div class="h-10 w-10 overflow-hidden rounded-full">
                <img :src="currentHousekeeper.avatar " class="h-full w-full object-cover" @error="$event.target.src = '/public/LOGO金刚(圆).png'">
              </div>
              <div>
                <div class="text-lg font-medium">
                  {{ currentHousekeeper.name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ currentHousekeeper.age }}岁 | {{ currentHousekeeper.experience }}年经验
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="h-full flex">
          <!-- 左侧：简历详情 -->
          <div class="w-1/2 overflow-y-auto border-r p-6">
            <div class="mb-6">
              <div class="mb-4 border-b pb-2 text-lg font-medium">
                基本信息
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">姓名：</span>
                  <span>{{ currentHousekeeper.name }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">年龄：</span>
                  <span>{{ currentHousekeeper.age }}岁</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">工作经验：</span>
                  <span>{{ currentHousekeeper.experience }}年</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">状态：</span>
                  <el-tag :type="currentHousekeeper?.statusType as 'success'|'warning'|'info'|'primary'|'danger'" size="small">
                    {{ currentHousekeeper?.status }}
                  </el-tag>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">籍贯：</span>
                  <span>{{ currentHousekeeper.hometowen }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500">学历：</span>
                  <span>{{ currentHousekeeper.education_name }}</span>
                </div>
              </div>
            </div>
            <div class="mb-6">
              <div class="mb-4 border-b pb-2 text-lg font-medium">
                技能特长
              </div>
              <div class="flex flex-wrap gap-2">
                <el-tag v-for="(skill, index) in ['做饭', '照顾老人', '照顾小孩', '做家务']" :key="index" size="small">
                  {{ skill }}
                </el-tag>
              </div>
            </div>
            <div class="mb-4 border-b pb-2 text-lg font-medium">
              证书资质
            </div>
            <div class="flex flex-wrap gap-4">
              <div class="w-32 overflow-hidden border rounded-lg">
                <img src="https://ai-public.mastergo.com/ai/img_res/832df97690c8ceb5af6c1aa3597a087b.jpg" class="h-24 w-full object-cover">
                <div class="p-2 text-center text-sm">
                  健康证
                </div>
              </div>
              <div class="w-32 overflow-hidden border rounded-lg">
                <img src="https://ai-public.mastergo.com/ai/img_res/a8cc0165c34c3909e302ede40d08df51.jpg" class="h-24 w-full object-cover">
                <div class="p-2 text-center text-sm">
                  月嫂证
                </div>
              </div>
            </div>
            <!-- 底部按钮组 -->
            <div class="h-16 flex justify-center gap-4 border-t bg-white p-4">
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="$router.push(`/housekeeping/housekeeper_detail/${currentHousekeeper.uuid}`)">
                <el-icon class="mr-1">
                  <Edit />
                </el-icon>编辑简历
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Share />
                </el-icon>分享简历
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap">
                更改上户状态
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Delete />
                </el-icon>删除
              </el-button>
            </div>
          </div>
          <div class="relative w-1/2 overflow-y-auto p-6">
            <el-tabs v-model="activeTab" class="demo-tabs">
              <el-tab-pane label="跟进记录" name="followup">
                <div class="mb-4 flex items-center justify-between">
                  <div class="text-lg font-medium">
                    跟进记录
                  </div>
                </div>

                <div v-for="record in followupRecords" :key="record.id" class="mb-4 border-b pb-4">
                  <div class="mb-2 flex justify-between">
                    <div class="font-medium">
                      {{ record.date }}
                    </div>
                    <div class="text-gray-500">
                      {{ record.creator }}
                    </div>
                  </div>
                  <div class="text-gray-600">
                    {{ record.content }}
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="客户评价" name="evaluation">
                <div class="mb-4 flex items-center justify-between">
                  <div class="text-lg font-medium">
                    客户评价
                  </div>
                </div>

                <div v-for="evaluation in evaluations" :key="evaluation.id" class="mb-4 border-b pb-4">
                  <div class="mb-2 flex justify-between">
                    <div class="font-medium">
                      {{ evaluation.customer }}
                    </div>
                    <div class="text-gray-500">
                      {{ evaluation.date }}
                    </div>
                  </div>
                  <div class="mb-2">
                    <el-rate v-model="evaluation.rating" disabled />
                  </div>
                  <div class="text-gray-600">
                    {{ evaluation.content }}
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <!-- 底部按钮组 -->
            <div class="absolute bottom-0 left-0 right-0 flex justify-center gap-4 border-t bg-white p-4">
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showFollowupDialog = true">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加跟进
              </el-button>
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showEvaluationDialog = true">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>添加评价
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Message />
                </el-icon>邀请客户评价
              </el-button>
            </div>
          </div>
        </div>
      </el-drawer>
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 flex flex-wrap gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">搜索</span>
            <el-select class="w-24">
              <el-option label="全部" value="all" />
              <el-option label="姓名" value="name" />
              <el-option label="电话" value="phone" />
            </el-select>
            <el-input v-model="searchForm.search" placeholder="请输入姓名、电话等关键字" class="w-64">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">类型</span>
            <el-select v-model="searchForm.type" placeholder="所有类型" class="w-50">
              <el-option
                v-for="item in formList.aunt_type"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
            <span class="text-gray-600">状态</span>
            <el-select v-model="searchForm.status" placeholder="所有状态" class="w-50">
              <el-option
                v-for="item in formList.status"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
            <span class="text-gray-600">创建人</span>
            <el-select v-model="searchForm.user_uuid" placeholder="不限" class="w-50">
              <el-option
                v-for="item in formList.create_users"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <div class="mb-4 flex flex-wrap gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">门店:</span>
            <el-select v-model="searchForm.store_uuid" placeholder="门店" class="w-50">
              <el-option
                v-for="item in formList.store_list"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">简历范围:</span>
            <el-select v-model="searchForm.resumeRange" placeholder="简历范围" class="w-50">
              <el-option label="所有状态" value="" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">现住区域:</span>
            <el-select v-model="searchForm.resumeRange" placeholder="请选择现住区域" class="w-50">
              <el-option label="现住区域" value="" />
            </el-select>
          </div>
        </div>

        <div v-show="showMoreFilters">
          <div class="mb-4 flex items-center gap-4">
            <span class="text-gray-600">籍贯:</span>
            <el-radio-group v-model="searchForm.hometown" size="large">
              <el-radio-button label="all">
                不限
              </el-radio-button>
              <el-radio-button label="north">
                要北方阿姨
              </el-radio-button>
              <el-radio-button label="south">
                要南方阿姨
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">年龄:</span>
              <el-radio-group v-model="searchForm.age" size="large">
                <el-radio-button v-for="age in formList.age" :key="age.value" :label="age.value">
                  {{ age.name }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <!-- 住家 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">住家:</span>
              <el-checkbox-group v-model="searchForm.can_live_home" size="large">
                <el-checkbox-button v-for="house in formList.live_home" :key="house.value" :label="house.value">
                  {{ house.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
          <!-- 技能 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600" style="white-space: nowrap;">技能:</span>
              <el-checkbox-group v-model="searchForm.aunt_skill" size="large" style="flex-wrap: wrap;">
                <el-checkbox-button v-for="skill in formList.aunt_skill" :key="skill.value" :label="skill.value" class="!rounded-button whitespace-nowrap">
                  {{ skill.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
          <!-- 生肖 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">属相:</span>
              <el-checkbox-group v-model="searchForm.chinese_zodiac" size="large">
                <el-checkbox-button v-for="zodiac in formList.chinese_zodiacs" :key="zodiac.value" :label="zodiac.value">
                  {{ zodiac.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
          <!-- 星座 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">星座:</span>
              <el-select v-model="searchForm.zodiac" placeholder="星座" class="w-32">
                <el-option
                  v-for="item in formList.zodiacs"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-600">性别:</span>
              <el-select v-model="searchForm.sex" placeholder="性别" class="w-32">
                <el-option
                  v-for="item in formList.sex"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-600">民族:</span>
              <el-select v-model="searchForm.nation" placeholder="民族" class="w-32">
                <el-option
                  v-for="item in formList.nation"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <!-- 学历 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">学历:</span>
              <el-select v-model="searchForm.education" placeholder="学历" class="w-32">
                <el-option
                  v-for="item in formList.educations"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-600">级别:</span>
              <el-select v-model="searchForm.level" placeholder="级别" class="w-32">
                <el-option
                  v-for="item in formList.level"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-gray-600">薪资范围:</span>
              <el-select v-model="searchForm.salary" placeholder="薪资范围" class="w-32">
                <el-option label="不限" value="" />
                <el-option label="3000以下" value="3000" />
                <el-option label="3000-5000" value="30005000" />
                <el-option label="5000-8000" value="5000-8000" />
                <el-option label="8000以上" value=">8000" />
              </el-select>
            </div>
          </div>
          <!-- 录入时间 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">录入时间:</span>
              <el-radio-group v-model="searchForm.create_time" size="large">
                <el-radio-button v-for="item in formList.create_time" :key="item.value" :label="item.value">
                  {{ item.name }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <!-- 更新时间 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">更新时间:</span>
              <el-radio-group v-model="searchForm.update_time" size="large">
                <el-radio-button label="all">
                  不限
                </el-radio-button>
                <el-radio-button label="today">
                  今天
                </el-radio-button>
                <el-radio-button label="yesterday">
                  昨天
                </el-radio-button>
                <el-radio-button label="this_week">
                  本周
                </el-radio-button>
                <el-radio-button label="last_week">
                  上周
                </el-radio-button>
                <el-radio-button label="this_month">
                  本月
                </el-radio-button>
                <el-radio-button label="last_month">
                  上月
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <!-- 保险状态 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">保险状态:</span>
              <el-radio-group v-model="searchForm.iStatus" size="large">
                <el-radio-button v-for="iStatus in formList.iStatus" :key="iStatus.value" :label="iStatus.value">
                  {{ iStatus.name }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <!-- 来源 -->
          <div class="mb-4 flex flex-wrap gap-4">
            <div class="flex items-center gap-2">
              <span class="text-gray-600">住家:</span>
              <el-checkbox-group v-model="searchForm.aunt_source" size="large">
                <el-checkbox-button v-for="source in formList.aunt_source" :key="source.value" :label="source.value">
                  {{ source.name }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <el-button class="!rounded-button whitespace-nowrap" @click="showMoreFilters = !showMoreFilters">
              {{ showMoreFilters ? '收起' : '更多筛选条件' }}
              <el-icon class="ml-1">
                <component :is="showMoreFilters ? ArrowUp : ArrowDown" />
              </el-icon>
            </el-button>
          </div>
          <div class="flex items-center gap-4">
            <el-button class="!rounded-button whitespace-nowrap" @click="resetSearchForm">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="getResumeList()">
              查询
            </el-button>
          </div>
        </div>
      </div>
      <div class="mb-4 flex items-center justify-between">
        <div class="text-gray-600">
          当前第 1 - 6 条，共计 6 条
        </div>
        <div class="flex items-center gap-6">
          <el-button class="!rounded-button whitespace-nowrap">
            年龄
            <el-icon class="ml-1">
              <Sort />
            </el-icon>
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap">
            经验
            <el-icon class="ml-1">
              <Sort />
            </el-icon>
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap">
            简历更新时间
            <el-icon class="ml-1">
              <Sort />
            </el-icon>
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap">
            录入时间
            <el-icon class="ml-1">
              <Sort />
            </el-icon>
          </el-button>
        </div>
      </div>
      <div class="grid grid-cols-3 gap-6">
        <div v-for="item in resumelist" :key="item.id" class="rounded-lg bg-white p-4 shadow-sm">
          <div class="mb-4 flex items-start gap-4">
            <div class="h-16 w-16 overflow-hidden rounded-lg">
              <img :src="item.avatar " class="h-full w-full object-cover" @error="$event.target.src = '/public/LOGO金刚(圆).png'">
            </div>
            <div class="flex-1">
              <div class="mb-2 flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <span class="text-lg font-medium">{{ item.name }}</span>
                  <span class="text-gray-500">{{ item.age }}岁 | {{ item.experience }}年经验</span>
                </div>
                <el-tag :type="item.statusType as 'success'|'warning'|'info'|'primary'|'danger'" size="small">
                  {{ item.status }}
                </el-tag>
              </div>
              <div class="text-gray-600">
                {{ item.hometown }}
              </div>
              <div class="text-gray-600">
                {{ item.type }}
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between border-t pt-4">
            <el-button text class="!rounded-button whitespace-nowrap px-2" @click="openDrawer(item)">
              <el-icon>
                <View />
              </el-icon>查看
            </el-button>
            <el-button text class="!rounded-button whitespace-nowrap px-2" @click="$router.push(`/housekeeping/housekeeper_detail/${item.uuid}`)">
              <el-icon>
                <Edit />
              </el-icon>编辑
            </el-button>
            <el-button text class="!rounded-button whitespace-nowrap px-2" @click="handleShare(item)">
              <el-icon>
                <Share />
              </el-icon>分享
            </el-button>
            <el-button text class="!rounded-button whitespace-nowrap px-2" @click="$router.push(`/housekeeping/housekeeper_print/${item.uuid}`)">
              <el-icon>
                <Printer />
              </el-icon>打印
            </el-button>
            <el-button text class="!rounded-button whitespace-nowrap px-2" @click="handleCollect(item)">
              <el-icon>
                <Star :style="{ color: item.is_collect ? '#F7BA2A' : '' }" />
              </el-icon>{{ item.is_collect ? '取消收藏' : '收藏' }}
            </el-button>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <div class="mt-6 flex items-center justify-between">
        <div class="text-gray-600">
          每页显示：
          <el-select v-model="pageSize" size="small" class="w-20" @change="handleSizeChange">
            <el-option :value="16" label="16" />
            <el-option :value="32" label="32" />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import housekeeperList from '@/api/modules/housekeeping/housekeeper_list'
import { ArrowDown, ArrowUp, Delete, Document, Edit, Plus, Printer, Search, Share, Sort, Star, Upload, View } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

onMounted(() => {
  getResumeList(),
  getField()
})
const checkboxGroup1 = ref()
const checkboxGroup2 = ref()
const checkboxGroup3 = ref()
const resumelist = ref([])
const showMoreFilters = ref(false) // 更多筛选条件
const showImportDialog = ref(false) // 批量导入
const showEvaluationDialog = ref(false) // 添加评价
const showAddDialog = ref(false)
const showFollowupDialog = ref(false)
const searchForm = ref({
  search: '',
  type: '',
  sort_field: '',
  sort_type: '',
  status: '',
  user_uuid: '0',
  age: '',
  minage: '',
  maxage: '',
  hometown: '',
  hometown_city: '',
  province_id: '',
  city_id: '',
  area_id: '',
  can_live_home: checkboxGroup1.value,
  aunt_skill: checkboxGroup3.value,
  chinese_zodiac: checkboxGroup2.value,
  zodiac: '',
  sex: '',
  nation: '',
  education: '',
  level: '',
  salary: '',
  salary1: '',
  salary2: '',
  createTime: '',
  create_time: '',
  religion: '',
  importVisible: '',
  insurance_status: '',
  search_type: '',
  update_time: '',
  updateTime: '',
  aunt_source: '',
  searchType: '',
})
const currentPage = ref(1)
const pageSize = ref(16)
const total = ref(0)
const formList = ref({})
const importForm = ref({
  file: null,
  account: '',
})
// 获取阿姨列表
async function getResumeList() {
  try {
    // 构建查询参数
    let queryParams = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchForm.value.search || '',
      search_type: searchForm.value.searchType || '',
      type: searchForm.value.type || '',
      status: searchForm.value.status || '',
      user_uuid: searchForm.value.user_uuid || '0',
      store_uuid: searchForm.value.store_uuid || '',
      sort_field: searchForm.value.sort_field || '',
      sort_type: searchForm.value.sort_type || ''
    }

    // 添加高级搜索条件
    if (showMoreFilters.value) {
      // 年龄范围
      if (searchForm.value.age) {
        const ageRanges = {
          '1': { min: 18, max: 30 },
          '2': { min: 31, max: 40 },
          '3': { min: 41, max: 50 },
          '4': { min: 51, max: 60 }
        }
        if (ageRanges[searchForm.value.age]) {
          queryParams.minage = ageRanges[searchForm.value.age].min
          queryParams.maxage = ageRanges[searchForm.value.age].max
        }
      }

      // 类型和技能
      if (searchForm.value.aunt_skill && searchForm.value.aunt_skill.length > 0) {
        queryParams.aunt_skill = searchForm.value.aunt_skill.join(',')
      }

      // 类型和技能
      if (searchForm.value.can_live_home && searchForm.value.can_live_home.length > 0) {
        queryParams.can_live_home = searchForm.value.can_live_home.join(',')
      }

      // 生肖
      if (searchForm.value.chinese_zodiac && searchForm.value.chinese_zodiac.length > 0) {
        queryParams.chinese_zodiac = searchForm.value.chinese_zodiac.join(',')
      }

      // 星座
      if (searchForm.value.zodiac) {
        queryParams.zodiac = searchForm.value.zodiac
      }

      // 性别
      if (searchForm.value.sex) {
        queryParams.sex = searchForm.value.sex
      }

      // 民族
      if (searchForm.value.nation) {
        queryParams.nation = searchForm.value.nation
      }

      // 学历
      if (searchForm.value.education) {
        queryParams.education = searchForm.value.education
      }

      // 级别
      if (searchForm.value.level) {
        queryParams.level = searchForm.value.level
      }

      // 薪资范围
      if (searchForm.value.salary) {
        queryParams.salary = searchForm.value.salary
      }

      // 录入时间
      if (searchForm.value.create_time) {
        queryParams.create_time = searchForm.value.create_time
      }

      // 更新时间
      if (searchForm.value.update_time) {
        queryParams.update_time = searchForm.value.update_time
      }

      // 保险状态
      if (searchForm.value.iStatus) {
        queryParams.insurance_status = searchForm.value.iStatus
      }

      // 来源
      if (searchForm.value.aunt_source && searchForm.value.aunt_source.length > 0) {
        queryParams.aunt_source = searchForm.value.aunt_source.join(',')
      }
    }

    // 调用API获取数据
    const response = await housekeeperList.resumeList(queryParams)

    if (response.code === 200 && response.data) {
      resumelist.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取阿姨列表失败')
      resumelist.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取阿姨列表失败:', error)
    ElMessage.error('获取阿姨列表失败')
    resumelist.value = []
    total.value = 0
  }
}
async function getField() {
  try {
    const response = await housekeeperList.getFieldsClass()
    if (response.code === 200) {
      formList.value = response.data || {}
    } else {
      ElMessage.error(response.msg || '获取字段配置失败')
      formList.value = {}
    }
  } catch (error) {
    console.error('获取字段配置失败:', error)
    ElMessage.error('获取字段配置失败')
    formList.value = {}
  }
}

function beforeUpload(file: File) {
  const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isXLSX) {
    ElMessage.error('只能上传 xlsx 格式的文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过 2MB!')
    return false
  }
  return true
}

function handleFileChange(file: any) {
  importForm.value.file = file.raw
}

async function handleImport() {
  if (!importForm.value.file) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  if (!importForm.value.account) {
    ElMessage.warning('请选择数据归属账号')
    return
  }

  try {
    ElMessage.success('导入成功')
    showImportDialog.value = false
  }
  catch (error) {
    ElMessage.error('导入失败')
  }
}

const followupForm = reactive({
  content: '',
  setReminder: false,
  reminderType: 'followup',
  reminderTime: '',
})

const evaluationForm = reactive({
  rating: 5,
  content: '',
  images: [],
})

async function handleFollowupSubmit() {
  try {
    if (!followupForm.content) {
      ElMessage.warning('请输入跟进内容')
      return
    }

    if (!currentHousekeeper.value || !currentHousekeeper.value.uuid) {
      ElMessage.warning('无法获取阿姨信息')
      return
    }

    // 构建请求参数
    const params = {
      aunt_uuid: currentHousekeeper.value.uuid,
      identity_type: 1, // 默认身份类型
      content: followupForm.content,
      images: [], // 图片数组
      set_reminder: followupForm.setReminder ? 1 : 0,
      reminder_type: followupForm.reminderType || '',
      reminder_time: followupForm.reminderTime ? new Date(followupForm.reminderTime).toISOString() : ''
    }

    // 调用API保存跟进记录
    const response = await housekeeperList.saveFollow(params.aunt_uuid, params.identity_type)

    if (response.code === 200) {
      showFollowupDialog.value = false
      ElMessage.success('添加跟进成功')

      // 重新获取跟进记录
      await getAuntFollowRecords(currentHousekeeper.value.uuid)

      // 重置表单
      followupForm.content = ''
      followupForm.setReminder = false
      followupForm.reminderType = 'followup'
      followupForm.reminderTime = ''
    } else {
      ElMessage.error(response.msg || '添加跟进失败')
    }
  } catch (error) {
    console.error('添加跟进失败:', error)
    ElMessage.error('添加跟进失败')
  }
}

function handleFollowupImageChange(file) {
  // TODO: 处理跟进图片上传逻辑
  console.log('跟进图片上传:', file)
}

async function handleEvaluationSubmit() {
  try {
    if (!evaluationForm.content) {
      ElMessage.warning('请输入评价内容')
      return
    }

    if (!currentHousekeeper.value || !currentHousekeeper.value.uuid) {
      ElMessage.warning('无法获取阿姨信息')
      return
    }

    // 模拟评价添加成功
    // 如果有评价相关的API，可以替换为实际调用

    // 添加到评价列表
    const newEvaluation = {
      id: Date.now(), // 使用时间戳作为临时ID
      date: new Date().toISOString().split('T')[0],
      content: evaluationForm.content,
      rating: evaluationForm.rating,
      customer: '当前用户', // 实际应使用登录用户名
      images: [...evaluationForm.images]
    }

    evaluations.value.unshift(newEvaluation)

    showEvaluationDialog.value = false
    ElMessage.success('添加评价成功')

    // 重置表单
    evaluationForm.content = ''
    evaluationForm.rating = 5
    evaluationForm.images = []
  } catch (error) {
    console.error('添加评价失败:', error)
    ElMessage.error('添加评价失败')
  }
}

function handleEvaluationImageChange(file) {
  // 处理评价图片上传逻辑
  console.log('评价图片上传:', file)
  evaluationForm.images.push(file.url || URL.createObjectURL(file.raw))
}
const formRef = ref()
const form = ref({
  idCardPhotos: '',
  idCardNo: '',
  name: '',
  birthday: '',
  gender: '',
  ethnicity: '',
  nativePlace: [],
  agreement: false,
  idAddress: '',
  tel: '',
})

const rules = {
  idCardPhotos: [{ required: true, message: '请上传身份证照片' }],
  idCardNo: [{ required: true, message: '请输入身份证号' }],
  name: [{ required: true, message: '请输入姓名' }],
  birthday: [{ required: true, message: '请选择生日' }],
  gender: [{ required: true, message: '请选择性别' }],
  ethnicity: [{ required: true, message: '请选择民族' }],
  nativePlace: [{ required: true, message: '请选择籍贯' }],
  agreement: [{ required: true, message: '请阅读并同意协议' }],
}

function handleIdCardUploadSuccess(response) {
  form.value.idCardPhotos = response.url
}

async function handleSubmit() {
  if (!formRef.value)
    return
  try {
    await formRef.value.validate()

    // 验证身份证号
    const idCheckResult = await housekeeperList.checkAuntIdNumber(form.value.idCardNo)
    if (idCheckResult.code !== 0) {
      ElMessage.error(idCheckResult.msg || '身份证号验证失败')
      return
    }

    // 构建请求数据
    const birthday = form.value.birthday ? new Date(form.value.birthday) : new Date()
    const formattedBirthday = `${birthday.getFullYear()}-${String(birthday.getMonth() + 1).padStart(2, '0')}-${String(birthday.getDate()).padStart(2, '0')}`

    const auntData = {
      basic_info: {
        name: form.value.name,
        id_card: form.value.idCardNo,
        gender: form.value.gender,
        birthday: formattedBirthday,
        ethnicity: form.value.ethnicity,
        native_place: form.value.nativePlace.join(','),
        mobile: form.value.tel,
        id_address: form.value.idAddress
      },
      images: [
        {
          type: 'id_card',
          url: form.value.idCardPhotos
        }
      ]
    }

    // 调用API创建阿姨
    const result = await housekeeperList.createAunt(auntData)
    if (result.code === 200) {
      showAddDialog.value = false
      ElMessage.success('保存成功')
      // 重新加载阿姨列表
      getResumeList()
    } else {
      ElMessage.error(result.msg || '保存失败')
    }
  } catch (error) {
    console.error('表单验证或提交失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  }
}
// 抽屉相关数据和方法
const drawerVisible = ref(false)
const currentHousekeeper = ref(null)
const activeTab = ref('followup')

// 打开抽屉方法
async function openDrawer(item) {
  try {
    // 先设置基本信息
    currentHousekeeper.value = item
    drawerVisible.value = true

    // 获取详细信息
    if (item && item.uuid) {
      // 构建请求参数
      const params = {
        number: item.number || '',
        uid: '0', // 默认用户ID
        xmjzfrom: 'xmjzpc' // 来源
      }

      // 调用API获取详细信息
      const response = await housekeeperList.resumeDetail(params)

      if (response.code === 200 && response.data && response.data.aunt) {
        // 更新当前阿姨信息
        const auntDetail = response.data.aunt

        // 合并详细信息到当前阿姨对象
        currentHousekeeper.value = {
          ...currentHousekeeper.value,
          ...auntDetail,
          // 确保关键字段存在
          name: auntDetail.name || currentHousekeeper.value.name,
          age: auntDetail.age || currentHousekeeper.value.age,
          experience: auntDetail.work_experience || currentHousekeeper.value.experience,
          avatar: auntDetail.avatar || currentHousekeeper.value.avatar,
          status: auntDetail.status_name || currentHousekeeper.value.status,
          hometowen: auntDetail.hometown || currentHousekeeper.value.hometowen,
          education_name: auntDetail.education_name || currentHousekeeper.value.education_name
        }

        // 获取跟进记录
        await getAuntFollowRecords(item.uuid)

        // 获取评价记录
        await getAuntEvaluations(item.uuid)
      } else {
        console.warn('获取阿姨详情失败:', response.msg)
      }
    }
  } catch (error) {
    console.error('打开阿姨详情失败:', error)
    ElMessage.error('获取阿姨详情失败')
  }
}

// 获取阿姨跟进记录
async function getAuntFollowRecords(auntUuid) {
  try {
    if (!auntUuid) return

    const params = {
      aunt_uuid: auntUuid,
      page: 1,
      size: 100
    }

    const response = await housekeeperList.getFollowList(params)

    if (response.code === 200 && response.data && response.data.list) {
      // 处理跟进记录数据
      followupRecords.value = response.data.list.map(item => ({
        id: item.id || item.uuid,
        date: item.create_time || '',
        content: item.content || '',
        creator: item.creator_name || ''
      }))
    } else {
      followupRecords.value = []
    }
  } catch (error) {
    console.error('获取跟进记录失败:', error)
    followupRecords.value = []
  }
}

// 获取阿姨评价记录
async function getAuntEvaluations(auntUuid) {
  try {
    if (!auntUuid) return

    // 模拟数据，实际应调用相应API
    // 如果有评价相关的API，可以替换为实际调用
    evaluations.value = [
      {
        id: 1,
        date: '2023-12-20',
        content: '做饭非常好吃，孩子很喜欢她做的菜，做事认真负责',
        rating: 5,
        customer: '刘女士',
      },
      {
        id: 2,
        date: '2023-11-15',
        content: '工作态度认真，但有时候沟通不够及时',
        rating: 4,
        customer: '张先生',
      },
    ]
  } catch (error) {
    console.error('获取评价记录失败:', error)
    evaluations.value = []
  }
}

// 模拟跟进记录数据
const followupRecords = ref([
  {
    id: 1,
    date: '2023-12-15',
    content: '客户对阿姨的工作表现很满意，特别是做饭和照顾孩子方面',
    creator: '张经理',
  },
  {
    id: 2,
    date: '2023-12-10',
    content: '阿姨反馈工作环境良好，与雇主沟通顺畅',
    creator: '李助理',
  },
  {
    id: 3,
    date: '2023-12-05',
    content: '初次上门，介绍了工作内容和注意事项',
    creator: '王主管',
  },
])

// 模拟评价数据
const evaluations = ref([
  {
    id: 1,
    date: '2023-12-20',
    content: '做饭非常好吃，孩子很喜欢她做的菜，做事认真负责',
    rating: 5,
    customer: '刘女士',
  },
  {
    id: 2,
    date: '2023-11-15',
    content: '工作态度认真，但有时候沟通不够及时',
    rating: 4,
    customer: '张先生',
  },
])
const showShareDialog = ref(false)
const shareLink = ref('https://example.com/housekeeper/123')

async function handleShare(item) {
  try {
    // 设置当前阿姨
    currentHousekeeper.value = item

    // 生成分享链接
    // 实际应调用相关API获取分享链接
    const baseUrl = window.location.origin
    shareLink.value = `${baseUrl}/share/aunt/${item.uuid || ''}`

    // 显示分享对话框
    showShareDialog.value = true
  } catch (error) {
    console.error('分享阿姨失败:', error)
    ElMessage.error('分享阿姨失败')
  }
}

function copyShareLink() {
  navigator.clipboard.writeText(shareLink.value)
  ElMessage.success('复制成功')
}

// 处理页面切换
function handlePageChange(page) {
  currentPage.value = page
  getResumeList()
}

// 处理每页显示数量变化
function handleSizeChange(size) {
  pageSize.value = size
  // 切换每页显示数量时返回第一页
  currentPage.value = 1
  getResumeList()
}

// 重置搜索条件
function resetSearchForm() {
  // 重置基本搜索条件
  searchForm.value = {
    search: '',
    type: '',
    sort_field: '',
    sort_type: '',
    status: '',
    user_uuid: '0',
    age: '',
    minage: '',
    maxage: '',
    hometown: '',
    hometown_city: '',
    province_id: '',
    city_id: '',
    area_id: '',
    can_live_home: [],
    aunt_skill: [],
    chinese_zodiac: [],
    zodiac: '',
    sex: '',
    nation: '',
    education: '',
    level: '',
    salary: '',
    salary1: '',
    salary2: '',
    createTime: '',
    create_time: '',
    religion: '',
    importVisible: '',
    insurance_status: '',
    search_type: '',
    update_time: '',
    updateTime: '',
    aunt_source: '',
    searchType: '',
  }

  // 重置页码
  currentPage.value = 1

  // 重新获取数据
  getResumeList()
}

// 收藏/取消收藏阿姨
async function handleCollect(item) {
  try {
    if (!item || !item.uuid) {
      ElMessage.warning('无法获取阿姨信息')
      return
    }

    // 当前收藏状态
    const isCollected = item.is_collect ? true : false

    // 调用API收藏/取消收藏
    const response = await housekeeperList.collectAunt(
      item.uuid,
      isCollected ? '0' : '1' // 1表示收藏，0表示取消收藏
    )

    if (response.code === 200) {
      // 更新当前项的收藏状态
      item.is_collect = !isCollected

      ElMessage.success(isCollected ? '取消收藏成功' : '收藏成功')
    } else {
      ElMessage.error(response.msg || (isCollected ? '取消收藏失败' : '收藏失败'))
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    ElMessage.error('收藏操作失败')
  }
}
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e5e7eb inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.el-select :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e5e7eb inset;
}

.el-select :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style>
