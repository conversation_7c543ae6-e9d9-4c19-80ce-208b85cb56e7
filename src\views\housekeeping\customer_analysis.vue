<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">客户分析</h1>

      <!-- 筛选区域 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">时间范围</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-[360px]"
            />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">门店</span>
            <el-select v-model="selectedStore" placeholder="选择门店" class="w-[200px]">
              <el-option label="全部门店" value="all" />
              <el-option
                v-for="store in storeList"
                :key="store.uuid"
                :label="store.name"
                :value="store.uuid"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-2">
            <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              查询
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据加载中 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <el-spinner size="large" />
    </div>

    <!-- 数据内容区 -->
    <div v-else>
      <!-- 数据概览卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div v-for="(card, index) in overviewCards" :key="index" class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-gray-500 text-sm mb-1">{{ card.title }}</div>
              <div class="text-2xl font-bold">{{ card.value }}</div>
            </div>
            <div :class="`text-${card.color}-500 text-lg`">
              <span>{{ card.change >= 0 ? '+' : '' }}{{ card.change }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户增长趋势 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h2 class="text-lg font-semibold mb-4">客户增长趋势</h2>
        <div class="h-80" ref="growthChartRef"></div>
      </div>

      <!-- 客户来源分布和地域分布 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">客户来源分布</h2>
          <div class="h-80" ref="sourceChartRef"></div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">客户地域分布</h2>
          <div class="h-80" ref="regionChartRef"></div>
        </div>
      </div>

      <!-- 客户消费分析和留存率 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">客户消费分析</h2>
          <div class="h-80" ref="consumptionChartRef"></div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
          <h2 class="text-lg font-semibold mb-4">客户留存率</h2>
          <div class="h-80" ref="retentionChartRef"></div>
        </div>
      </div>

      <!-- 客户活跃度分析 -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <h2 class="text-lg font-semibold mb-4">客户活跃度分析</h2>
        <div class="h-80" ref="activityChartRef"></div>
      </div>

      <!-- 导出按钮 -->
      <div class="flex justify-end mb-6">
        <el-button type="primary" @click="exportData">导出分析数据</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import customerAnalysisApi from '@/api/modules/housekeeping/customer_analysis';
import * as echarts from 'echarts';

// 筛选条件
const dateRange = ref([]);
const selectedStore = ref('all');

// 加载状态
const loading = ref(false);

// 门店列表
const storeList = ref([]);

// 概览数据
const overviewCards = ref([
  { title: '总客户数', value: '0', change: 0, color: 'blue' },
  { title: '新增客户数', value: '0', change: 0, color: 'green' },
  { title: '客户平均消费', value: '¥0', change: 0, color: 'orange' },
  { title: '客户活跃度', value: '0%', change: 0, color: 'purple' },
]);

// 图表引用
const growthChartRef = ref(null);
const sourceChartRef = ref(null);
const regionChartRef = ref(null);
const consumptionChartRef = ref(null);
const retentionChartRef = ref(null);
const activityChartRef = ref(null);

// 图表实例
let growthChart = null;
let sourceChart = null;
let regionChart = null;
let consumptionChart = null;
let retentionChart = null;
let activityChart = null;

// 获取门店列表
async function getStoreList() {
  try {
    const response = await customerAnalysisApi.getStoreList();

    if (response.code === 200 && response.data) {
      storeList.value = response.data.list || [];
    } else {
      storeList.value = [];
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    storeList.value = [];
  }
}

// 获取客户分析概览数据
async function getCustomerAnalysisOverview() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerAnalysisOverview(params);

    if (response.code === 200 && response.data) {
      // 更新概览数据
      overviewCards.value[0].value = response.data.total_customers || '0';
      overviewCards.value[0].change = response.data.total_customers_change || 0;

      overviewCards.value[1].value = response.data.new_customers || '0';
      overviewCards.value[1].change = response.data.new_customers_change || 0;

      overviewCards.value[2].value = `¥${response.data.average_consumption || '0'}`;
      overviewCards.value[2].change = response.data.average_consumption_change || 0;

      overviewCards.value[3].value = `${response.data.activity_rate || '0'}%`;
      overviewCards.value[3].change = response.data.activity_rate_change || 0;
    }
  } catch (error) {
    console.error('获取客户分析概览数据失败:', error);
  }
}

// 获取客户增长趋势数据
async function getCustomerGrowthTrend() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerGrowthTrend(params);

    if (response.code === 200 && response.data) {
      // 初始化客户增长趋势图表
      initGrowthChart(response.data);
    }
  } catch (error) {
    console.error('获取客户增长趋势数据失败:', error);
  }
}

// 获取客户来源分布数据
async function getCustomerSourceDistribution() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerSourceDistribution(params);

    if (response.code === 200 && response.data) {
      // 初始化客户来源分布图表
      initSourceChart(response.data);
    }
  } catch (error) {
    console.error('获取客户来源分布数据失败:', error);
  }
}

// 获取客户地域分布数据
async function getCustomerRegionDistribution() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerRegionDistribution(params);

    if (response.code === 200 && response.data) {
      // 初始化客户地域分布图表
      initRegionChart(response.data);
    }
  } catch (error) {
    console.error('获取客户地域分布数据失败:', error);
  }
}

// 获取客户消费分析数据
async function getCustomerConsumptionAnalysis() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerConsumptionAnalysis(params);

    if (response.code === 200 && response.data) {
      // 初始化客户消费分析图表
      initConsumptionChart(response.data);
    }
  } catch (error) {
    console.error('获取客户消费分析数据失败:', error);
  }
}

// 获取客户留存率数据
async function getCustomerRetentionRate() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerRetentionRate(params);

    if (response.code === 200 && response.data) {
      // 初始化客户留存率图表
      initRetentionChart(response.data);
    }
  } catch (error) {
    console.error('获取客户留存率数据失败:', error);
  }
}

// 获取客户活跃度分析数据
async function getCustomerActivityAnalysis() {
  try {
    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.getCustomerActivityAnalysis(params);

    if (response.code === 200 && response.data) {
      // 初始化客户活跃度分析图表
      initActivityChart(response.data);
    }
  } catch (error) {
    console.error('获取客户活跃度分析数据失败:', error);
  }
}

// 格式化日期
function formatDate(date, isEndOfDay = false) {
  if (!date) return '';

  const d = new Date(date);

  if (isEndOfDay) {
    // 设置为当天的最后一刻 (23:59:59.999)
    d.setHours(23, 59, 59, 999);
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 初始化客户增长趋势图表
function initGrowthChart(data) {
  if (!growthChartRef.value) return;

  if (!growthChart) {
    growthChart = echarts.init(growthChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['新增客户', '总客户数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dates || []
    },
    yAxis: [
      {
        type: 'value',
        name: '新增客户',
        position: 'left'
      },
      {
        type: 'value',
        name: '总客户数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '新增客户',
        type: 'bar',
        data: data.new_customers || []
      },
      {
        name: '总客户数',
        type: 'line',
        yAxisIndex: 1,
        data: data.total_customers || []
      }
    ]
  };

  growthChart.setOption(option);
}

// 初始化客户来源分布图表
function initSourceChart(data) {
  if (!sourceChartRef.value) return;

  if (!sourceChart) {
    sourceChart = echarts.init(sourceChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.sources?.map(item => item.name) || []
    },
    series: [
      {
        name: '客户来源',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data.sources?.map(item => ({
          value: item.value,
          name: item.name
        })) || []
      }
    ]
  };

  sourceChart.setOption(option);
}

// 初始化客户地域分布图表
function initRegionChart(data) {
  if (!regionChartRef.value) return;

  if (!regionChart) {
    regionChart = echarts.init(regionChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.regions?.map(item => item.name) || []
    },
    series: [
      {
        name: '客户地域',
        type: 'pie',
        radius: '55%',
        center: ['50%', '60%'],
        data: data.regions?.map(item => ({
          value: item.value,
          name: item.name
        })) || [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  regionChart.setOption(option);
}

// 初始化客户消费分析图表
function initConsumptionChart(data) {
  if (!consumptionChartRef.value) return;

  if (!consumptionChart) {
    consumptionChart = echarts.init(consumptionChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['客单价', '订单数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: data.consumption_ranges || []
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '客单价',
        position: 'left'
      },
      {
        type: 'value',
        name: '订单数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '客单价',
        type: 'bar',
        data: data.average_prices || []
      },
      {
        name: '订单数',
        type: 'line',
        yAxisIndex: 1,
        data: data.order_counts || []
      }
    ]
  };

  consumptionChart.setOption(option);
}

// 初始化客户留存率图表
function initRetentionChart(data) {
  if (!retentionChartRef.value) return;

  if (!retentionChart) {
    retentionChart = echarts.init(retentionChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: data.periods || []
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '留存率',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '留存率',
        type: 'bar',
        data: data.retention_rates || []
      }
    ]
  };

  retentionChart.setOption(option);
}

// 初始化客户活跃度分析图表
function initActivityChart(data) {
  if (!activityChartRef.value) return;

  if (!activityChart) {
    activityChart = echarts.init(activityChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['活跃客户', '非活跃客户', '活跃率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: data.dates || []
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '客户数',
        position: 'left'
      },
      {
        type: 'value',
        name: '活跃率',
        min: 0,
        max: 100,
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '活跃客户',
        type: 'bar',
        stack: '客户',
        data: data.active_customers || []
      },
      {
        name: '非活跃客户',
        type: 'bar',
        stack: '客户',
        data: data.inactive_customers || []
      },
      {
        name: '活跃率',
        type: 'line',
        yAxisIndex: 1,
        data: data.activity_rates || []
      }
    ]
  };

  activityChart.setOption(option);
}

// 导出分析数据
async function exportData() {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {
      store_uuid: selectedStore.value === 'all' ? undefined : selectedStore.value
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1], true);
    }

    const response = await customerAnalysisApi.exportCustomerAnalysisData(params);

    // 创建一个下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `客户分析数据_${new Date().getTime()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出分析数据失败:', error);
    ElMessage.error('导出分析数据失败');
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function handleReset() {
  dateRange.value = [];
  selectedStore.value = 'all';
  handleSearch();
}

// 查询数据
async function handleSearch() {
  try {
    loading.value = true;

    // 获取所有分析数据
    await Promise.all([
      getCustomerAnalysisOverview(),
      getCustomerGrowthTrend(),
      getCustomerSourceDistribution(),
      getCustomerRegionDistribution(),
      getCustomerConsumptionAnalysis(),
      getCustomerRetentionRate(),
      getCustomerActivityAnalysis()
    ]);
  } catch (error) {
    console.error('查询数据失败:', error);
    ElMessage.error('查询数据失败');
  } finally {
    loading.value = false;
  }
}

// 页面加载时获取数据
onMounted(() => {
  getStoreList();
  handleSearch();
});

// 页面卸载时释放图表实例
onUnmounted(() => {
  if (growthChart) growthChart.dispose();
  if (sourceChart) sourceChart.dispose();
  if (regionChart) regionChart.dispose();
  if (consumptionChart) consumptionChart.dispose();
  if (retentionChart) retentionChart.dispose();
  if (activityChart) activityChart.dispose();
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
