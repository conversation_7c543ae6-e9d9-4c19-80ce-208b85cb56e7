{"type": "module", "version": "5.1.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vue-tsc --skipLibCheck && vite build", "build:test": "vue-tsc -b && vite build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "preinstall": "npx only-allow pnpm", "taze": "taze minor -wIr", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vee-validate/zod": "^4.15.0", "@vueuse/components": "^12.8.2", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^12.8.2", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "echarts": "^5.4.2", "element-plus": "^2.9.6", "eruda": "^3.4.1", "es-toolkit": "^1.33.0", "hotkeys-js": "^3.13.9", "lucide-vue-next": "^0.479.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.1", "qs": "^6.14.0", "reka-ui": "^2.0.2", "scule": "^1.3.0", "tailwind-merge": "^3.0.2", "ua-parser-js": "^2.0.2", "vconsole": "^3.15.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-sonner": "^1.3.0", "zod": "^3.24.2"}, "devDependencies": {"@antfu/eslint-config": "4.3.0", "@faker-js/faker": "^9.6.0", "@iconify/json": "^2.2.315", "@iconify/vue": "^4.3.0", "@stylistic/stylelint-config": "^2.0.0", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@unocss/eslint-plugin": "^65.5.0", "@unocss/preset-legacy-compat": "^65.5.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "bumpp": "^10.0.3", "cz-git": "^1.11.1", "echarts": "^5.4.2", "eslint": "^9.22.0", "esno": "^4.8.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "inquirer": "^12.4.3", "lint-staged": "^15.4.3", "npm-run-all2": "^7.0.2", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.3", "postcss-nested": "^7.0.2", "sass-embedded": "^1.85.1", "stylelint": "^16.15.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.11.1", "svgo": "^3.3.2", "taze": "^18.7.1", "typescript": "^5.8.2", "unocss": "^65.5.0", "unocss-preset-animations": "^1.1.1", "unocss-preset-scrollbar": "^3.2.0", "unplugin-auto-import": "^19.1.1", "unplugin-turbo-console": "^1.11.3", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.1", "vite-plugin-app-loading": "^0.3.1", "vite-plugin-archiver": "^0.1.2", "vite-plugin-banner": "^0.8.0", "vite-plugin-compression2": "^1.3.3", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.32.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.2.8"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}