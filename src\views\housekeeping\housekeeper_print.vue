<template>
  <div class="min-h-screen bg-white p-8">
    <!-- 顶部按钮 -->
    <div class="mb-6 flex justify-end">
      <el-button type="primary" @click="window.print()">
        <el-icon class="mr-1">
          <Printer />
        </el-icon>打印
      </el-button>
    </div>

    <!-- 打印内容 -->
    <div class="mx-auto max-w-4xl">
      <!-- 头部信息 -->
      <div class="mb-8 bg-primary-500 p-6 text-white">
        <div class="text-center text-2xl font-bold mb-6">{{ store.name }}</div>
        <div class="flex items-center gap-4">
          <div class="h-24 w-24 overflow-hidden rounded-full bg-white">
            <img :src="housekeeper.avatar" class="h-full w-full object-cover" @error="$event.target.src = '/public/LOGO金刚(圆).png'">
          </div>
          <div>
            <div class="text-xl">{{ housekeeper.name }} <span class="text-base">({{ housekeeper.province }} | {{ housekeeper.age }}岁 | {{ housekeeper.experience }}年经验)</span></div>
            <div class="mt-2">类型：{{ housekeeper.type || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="mb-8 rounded-lg border p-6">
        <div class="mb-4 text-lg font-medium">基本信息</div>
        <div class="grid grid-cols-3 gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-500">性别：</span>
            <span>{{ housekeeper.gender }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">籍贯：</span>
            <span>{{ housekeeper.hometowen }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">身高：</span>
            <span>{{ housekeeper.height || '-' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">体重：</span>
            <span>{{ housekeeper.weight || '-' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">属相：</span>
            <span>{{ housekeeper.zodiac }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">学历：</span>
            <span>{{ housekeeper.education_name }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">星座：</span>
            <span>{{ housekeeper.constellation }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">血型：</span>
            <span>{{ housekeeper.blood_type || '-' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">民族：</span>
            <span>{{ housekeeper.nation || '-' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">签证：</span>
            <span>{{ housekeeper.visa || '-' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">婚姻状况：</span>
            <span>{{ housekeeper.marriage }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-500">住家：</span>
            <span>{{ housekeeper.live_in }}</span>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-start gap-2">
            <span class="text-gray-500">现住地址：</span>
            <span>{{ housekeeper.address }}</span>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-start gap-2">
            <span class="text-gray-500">身份证号：</span>
            <span>{{ housekeeper.id_card }}</span>
          </div>
        </div>
      </div>

      <!-- 技能特长 -->
      <div class="mb-8 rounded-lg border p-6">
        <div class="mb-4 text-lg font-medium">技能特长</div>
        <div class="flex flex-wrap gap-2">
          <el-tag v-for="skill in housekeeper.skills" :key="skill" size="small">
            {{ skill }}
          </el-tag>
        </div>
      </div>

      <!-- 证书资质 -->
      <div class="rounded-lg border p-6">
        <div class="mb-4 text-lg font-medium">证书资质</div>
        <div class="flex flex-wrap gap-4">
          <div v-for="cert in housekeeper.certificates" :key="cert.name" class="w-32 overflow-hidden rounded border">
            <img :src="cert.image" class="h-24 w-full object-cover">
            <div class="p-2 text-center text-sm">{{ cert.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Printer } from '@element-plus/icons-vue'

const route = useRoute()
const store = ref({
  name: '厦门小羽佳线索店'
})

const housekeeper = ref({
  uuid: route.params.uuid,
  name: 'yrs',
  avatar: '',
  age: 22,
  province: '福建',
  experience: 3,
  type: '',
  gender: '男',
  hometowen: '福建',
  height: '-',
  weight: '-',
  zodiac: '马',
  education_name: '初中',
  constellation: '射手座',
  blood_type: '-',
  nation: '-',
  visa: '-',
  marriage: '已婚',
  live_in: '住家',
  address: '福建省厦门市思明区湖滨东路319号人才市场内a栋6层601室',
  id_card: '******',
  skills: ['做饭', '照顾老人', '照顾小孩', '做家务'],
  certificates: [
    {
      name: '健康证',
      image: 'https://ai-public.mastergo.com/ai/img_res/832df97690c8ceb5af6c1aa3597a087b.jpg'
    },
    {
      name: '月嫂证',
      image: 'https://ai-public.mastergo.com/ai/img_res/a8cc0165c34c3909e302ede40d08df51.jpg'
    }
  ]
})

onMounted(() => {
  // TODO: 根据uuid获取家政员详细信息
})
</script>

<style>
@media print {
  @page {
    margin: 0;
    size: auto;
  }

  body {
    margin: 1.6cm;
  }

  .el-button {
    display: none;
  }
}
</style>