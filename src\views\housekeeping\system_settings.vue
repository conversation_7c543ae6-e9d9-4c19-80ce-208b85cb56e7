<template>
  <div class="container mx-auto p-4">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">系统设置</h1>

      <!-- 设置选项卡 -->
      <el-tabs v-model="activeTab" class="bg-white rounded-lg shadow">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <div class="p-4">
            <el-form
              ref="basicFormRef"
              :model="basicForm"
              :rules="basicRules"
              label-width="120px"
              v-loading="basicLoading"
            >
              <div class="flex mb-6">
                <div class="w-1/3 flex flex-col items-center">
                  <el-image
                    :src="logoUrl || defaultLogo"
                    fit="cover"
                    class="w-32 h-32 rounded-md mb-4"
                  />
                  <el-upload
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handleLogoChange"
                  >
                    <el-button type="primary">上传Logo</el-button>
                  </el-upload>
                </div>
                <div class="w-2/3">
                  <el-form-item label="系统名称" prop="systemName">
                    <el-input v-model="basicForm.systemName" placeholder="请输入系统名称" />
                  </el-form-item>
                  <el-form-item label="公司名称" prop="companyName">
                    <el-input v-model="basicForm.companyName" placeholder="请输入公司名称" />
                  </el-form-item>
                  <el-form-item label="联系电话" prop="contactPhone">
                    <el-input v-model="basicForm.contactPhone" placeholder="请输入联系电话" />
                  </el-form-item>
                </div>
              </div>

              <el-form-item label="公司地址" prop="companyAddress">
                <el-input v-model="basicForm.companyAddress" placeholder="请输入公司地址" />
              </el-form-item>
              <el-form-item label="系统版本" prop="systemVersion">
                <el-input v-model="basicForm.systemVersion" placeholder="请输入系统版本" />
              </el-form-item>
              <el-form-item label="备案号" prop="icp">
                <el-input v-model="basicForm.icp" placeholder="请输入备案号" />
              </el-form-item>
              <el-form-item label="系统描述">
                <el-input v-model="basicForm.systemDescription" type="textarea" :rows="3" placeholder="请输入系统描述" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveBasicSettings" :loading="basicSubmitting">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 支付设置 -->
        <el-tab-pane label="支付设置" name="payment">
          <div class="p-4">
            <el-form
              ref="paymentFormRef"
              :model="paymentForm"
              label-width="120px"
              v-loading="paymentLoading"
            >
              <h2 class="text-lg font-semibold mb-4">微信支付设置</h2>
              <el-form-item label="商户ID" prop="wxMerchantId">
                <el-input v-model="paymentForm.wxMerchantId" placeholder="请输入微信商户ID" />
              </el-form-item>
              <el-form-item label="商户密钥" prop="wxMerchantKey">
                <el-input v-model="paymentForm.wxMerchantKey" placeholder="请输入微信商户密钥" show-password />
              </el-form-item>
              <el-form-item label="微信AppID" prop="wxAppId">
                <el-input v-model="paymentForm.wxAppId" placeholder="请输入微信AppID" />
              </el-form-item>
              <el-form-item label="微信AppSecret" prop="wxAppSecret">
                <el-input v-model="paymentForm.wxAppSecret" placeholder="请输入微信AppSecret" show-password />
              </el-form-item>
              <el-form-item label="微信支付状态">
                <el-switch v-model="paymentForm.wxPayEnabled" />
              </el-form-item>

              <h2 class="text-lg font-semibold mb-4 mt-8">支付宝设置</h2>
              <el-form-item label="商户ID" prop="aliMerchantId">
                <el-input v-model="paymentForm.aliMerchantId" placeholder="请输入支付宝商户ID" />
              </el-form-item>
              <el-form-item label="商户密钥" prop="aliMerchantKey">
                <el-input v-model="paymentForm.aliMerchantKey" placeholder="请输入支付宝商户密钥" show-password />
              </el-form-item>
              <el-form-item label="支付宝AppID" prop="aliAppId">
                <el-input v-model="paymentForm.aliAppId" placeholder="请输入支付宝AppID" />
              </el-form-item>
              <el-form-item label="支付宝支付状态">
                <el-switch v-model="paymentForm.aliPayEnabled" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="savePaymentSettings" :loading="paymentSubmitting">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 短信设置 -->
        <el-tab-pane label="短信设置" name="sms">
          <div class="p-4">
            <el-form
              ref="smsFormRef"
              :model="smsForm"
              label-width="120px"
              v-loading="smsLoading"
            >
              <el-form-item label="短信服务商" prop="smsProvider">
                <el-select v-model="smsForm.smsProvider" placeholder="请选择短信服务商" class="w-full">
                  <el-option label="阿里云" value="aliyun" />
                  <el-option label="腾讯云" value="tencent" />
                </el-select>
              </el-form-item>
              <el-form-item label="AccessKey" prop="accessKey">
                <el-input v-model="smsForm.accessKey" placeholder="请输入AccessKey" />
              </el-form-item>
              <el-form-item label="AccessSecret" prop="accessSecret">
                <el-input v-model="smsForm.accessSecret" placeholder="请输入AccessSecret" show-password />
              </el-form-item>
              <el-form-item label="签名" prop="signName">
                <el-input v-model="smsForm.signName" placeholder="请输入短信签名" />
              </el-form-item>
              <el-form-item label="验证码模板" prop="verifyCodeTemplate">
                <el-input v-model="smsForm.verifyCodeTemplate" placeholder="请输入验证码模板编号" />
              </el-form-item>
              <el-form-item label="订单通知模板" prop="orderNotifyTemplate">
                <el-input v-model="smsForm.orderNotifyTemplate" placeholder="请输入订单通知模板编号" />
              </el-form-item>
              <el-form-item label="短信发送状态">
                <el-switch v-model="smsForm.smsEnabled" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveSmsSettings" :loading="smsSubmitting">保存设置</el-button>
                <el-button type="success" @click="openTestSmsDialog" :disabled="!smsForm.smsEnabled">测试发送</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <div class="p-4">
            <el-form
              ref="notificationFormRef"
              :model="notificationForm"
              label-width="180px"
              v-loading="notificationLoading"
            >
              <h2 class="text-lg font-semibold mb-4">微信模板消息设置</h2>
              <el-form-item label="订单创建通知" prop="orderCreateTemplate">
                <el-input v-model="notificationForm.orderCreateTemplate" placeholder="请输入订单创建通知模板编号" />
              </el-form-item>
              <el-form-item label="订单支付成功通知" prop="orderPayTemplate">
                <el-input v-model="notificationForm.orderPayTemplate" placeholder="请输入订单支付成功通知模板编号" />
              </el-form-item>
              <el-form-item label="服务完成通知" prop="serviceCompleteTemplate">
                <el-input v-model="notificationForm.serviceCompleteTemplate" placeholder="请输入服务完成通知模板编号" />
              </el-form-item>
              <el-form-item label="微信模板消息状态">
                <el-switch v-model="notificationForm.wxTemplateEnabled" />
              </el-form-item>

              <h2 class="text-lg font-semibold mb-4 mt-8">小程序订阅消息设置</h2>
              <el-form-item label="订单状态变更通知" prop="orderStatusTemplate">
                <el-input v-model="notificationForm.orderStatusTemplate" placeholder="请输入订单状态变更通知模板编号" />
              </el-form-item>
              <el-form-item label="服务评价提醒" prop="serviceEvaluationTemplate">
                <el-input v-model="notificationForm.serviceEvaluationTemplate" placeholder="请输入服务评价提醒模板编号" />
              </el-form-item>
              <el-form-item label="小程序订阅消息状态">
                <el-switch v-model="notificationForm.miniappSubscribeEnabled" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveNotificationSettings" :loading="notificationSubmitting">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 测试发送短信对话框 -->
    <el-dialog v-model="testSmsDialogVisible" title="测试发送短信" width="500px">
      <el-form :model="testSmsForm" label-width="100px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="testSmsForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="测试类型" prop="type">
          <el-select v-model="testSmsForm.type" placeholder="请选择测试类型" class="w-full">
            <el-option label="验证码" value="verifyCode" />
            <el-option label="订单通知" value="orderNotify" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="testSmsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendTestSms" :loading="testSmsLoading">发送测试</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import systemSettingsApi from '@/api/modules/housekeeping/system_settings';

// 默认Logo
const defaultLogo = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

// 激活的选项卡
const activeTab = ref('basic');

// 基本设置
const basicFormRef = ref(null);
const basicLoading = ref(false);
const basicSubmitting = ref(false);
const logoUrl = ref('');
const logoFile = ref(null);

// 支付设置
const paymentFormRef = ref(null);
const paymentLoading = ref(false);
const paymentSubmitting = ref(false);

// 短信设置
const smsFormRef = ref(null);
const smsLoading = ref(false);
const smsSubmitting = ref(false);

// 通知设置
const notificationFormRef = ref(null);
const notificationLoading = ref(false);
const notificationSubmitting = ref(false);

// 测试短信对话框
const testSmsDialogVisible = ref(false);
const testSmsLoading = ref(false);
const testSmsForm = reactive({
  phone: '',
  type: 'verifyCode'
});

// 基本设置表单
const basicForm = reactive({
  systemName: '',
  companyName: '',
  contactPhone: '',
  companyAddress: '',
  systemVersion: '',
  icp: '',
  systemDescription: '',
  logo: ''
});

// 支付设置表单
const paymentForm = reactive({
  wxMerchantId: '',
  wxMerchantKey: '',
  wxAppId: '',
  wxAppSecret: '',
  wxPayEnabled: false,
  aliMerchantId: '',
  aliMerchantKey: '',
  aliAppId: '',
  aliPayEnabled: false
});

// 短信设置表单
const smsForm = reactive({
  smsProvider: 'aliyun',
  accessKey: '',
  accessSecret: '',
  signName: '',
  verifyCodeTemplate: '',
  orderNotifyTemplate: '',
  smsEnabled: false
});

// 通知设置表单
const notificationForm = reactive({
  orderCreateTemplate: '',
  orderPayTemplate: '',
  serviceCompleteTemplate: '',
  wxTemplateEnabled: false,
  orderStatusTemplate: '',
  serviceEvaluationTemplate: '',
  miniappSubscribeEnabled: false
});

// 基本设置验证规则
const basicRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ]
};

// 获取基本设置
async function getSystemSettings() {
  try {
    basicLoading.value = true;

    const response = await systemSettingsApi.getSystemSettings();

    if (response.code === 200 && response.data) {
      basicForm.systemName = response.data.system_name || '';
      basicForm.companyName = response.data.company_name || '';
      basicForm.contactPhone = response.data.contact_phone || '';
      basicForm.companyAddress = response.data.company_address || '';
      basicForm.systemVersion = response.data.system_version || '';
      basicForm.icp = response.data.icp || '';
      basicForm.systemDescription = response.data.system_description || '';
      basicForm.logo = response.data.logo || '';

      // 设置Logo预览
      if (response.data.logo) {
        logoUrl.value = response.data.logo;
      }
    } else {
      ElMessage.error(response.msg || '获取基本设置失败');
    }
  } catch (error) {
    console.error('获取基本设置失败:', error);
    ElMessage.error('获取基本设置失败');
  } finally {
    basicLoading.value = false;
  }
}

// 获取支付设置
async function getPaymentSettings() {
  try {
    paymentLoading.value = true;

    const response = await systemSettingsApi.getPaymentSettings();

    if (response.code === 200 && response.data) {
      paymentForm.wxMerchantId = response.data.wx_merchant_id || '';
      paymentForm.wxMerchantKey = response.data.wx_merchant_key || '';
      paymentForm.wxAppId = response.data.wx_app_id || '';
      paymentForm.wxAppSecret = response.data.wx_app_secret || '';
      paymentForm.wxPayEnabled = response.data.wx_pay_enabled || false;
      paymentForm.aliMerchantId = response.data.ali_merchant_id || '';
      paymentForm.aliMerchantKey = response.data.ali_merchant_key || '';
      paymentForm.aliAppId = response.data.ali_app_id || '';
      paymentForm.aliPayEnabled = response.data.ali_pay_enabled || false;
    } else {
      ElMessage.error(response.msg || '获取支付设置失败');
    }
  } catch (error) {
    console.error('获取支付设置失败:', error);
    ElMessage.error('获取支付设置失败');
  } finally {
    paymentLoading.value = false;
  }
}

// 获取短信设置
async function getSmsSettings() {
  try {
    smsLoading.value = true;

    const response = await systemSettingsApi.getSmsSettings();

    if (response.code === 200 && response.data) {
      smsForm.smsProvider = response.data.sms_provider || 'aliyun';
      smsForm.accessKey = response.data.access_key || '';
      smsForm.accessSecret = response.data.access_secret || '';
      smsForm.signName = response.data.sign_name || '';
      smsForm.verifyCodeTemplate = response.data.verify_code_template || '';
      smsForm.orderNotifyTemplate = response.data.order_notify_template || '';
      smsForm.smsEnabled = response.data.sms_enabled || false;
    } else {
      ElMessage.error(response.msg || '获取短信设置失败');
    }
  } catch (error) {
    console.error('获取短信设置失败:', error);
    ElMessage.error('获取短信设置失败');
  } finally {
    smsLoading.value = false;
  }
}

// 获取通知设置
async function getNotificationSettings() {
  try {
    notificationLoading.value = true;

    const response = await systemSettingsApi.getNotificationSettings();

    if (response.code === 200 && response.data) {
      notificationForm.orderCreateTemplate = response.data.order_create_template || '';
      notificationForm.orderPayTemplate = response.data.order_pay_template || '';
      notificationForm.serviceCompleteTemplate = response.data.service_complete_template || '';
      notificationForm.wxTemplateEnabled = response.data.wx_template_enabled || false;
      notificationForm.orderStatusTemplate = response.data.order_status_template || '';
      notificationForm.serviceEvaluationTemplate = response.data.service_evaluation_template || '';
      notificationForm.miniappSubscribeEnabled = response.data.miniapp_subscribe_enabled || false;
    } else {
      ElMessage.error(response.msg || '获取通知设置失败');
    }
  } catch (error) {
    console.error('获取通知设置失败:', error);
    ElMessage.error('获取通知设置失败');
  } finally {
    notificationLoading.value = false;
  }
}

// 处理Logo变化
function handleLogoChange(file) {
  logoFile.value = file.raw;
  logoUrl.value = URL.createObjectURL(file.raw);
}

// 上传Logo
async function uploadLogo() {
  if (!logoFile.value) {
    return basicForm.logo;
  }

  try {
    const formData = new FormData();
    formData.append('file', logoFile.value);

    const response = await systemSettingsApi.uploadSystemLogo(formData);

    if (response.code === 200 && response.data) {
      return response.data.url;
    } else {
      throw new Error(response.msg || '上传Logo失败');
    }
  } catch (error) {
    console.error('上传Logo失败:', error);
    ElMessage.error('上传Logo失败');
    return basicForm.logo;
  }
}

// 保存基本设置
async function saveBasicSettings() {
  if (!basicFormRef.value) return;

  try {
    await basicFormRef.value.validate();

    basicSubmitting.value = true;

    // 上传Logo
    const logoUrl = await uploadLogo();

    // 构建请求数据
    const data = {
      system_name: basicForm.systemName,
      company_name: basicForm.companyName,
      contact_phone: basicForm.contactPhone,
      company_address: basicForm.companyAddress || undefined,
      system_version: basicForm.systemVersion || undefined,
      icp: basicForm.icp || undefined,
      system_description: basicForm.systemDescription || undefined,
      logo: logoUrl || undefined
    };

    const response = await systemSettingsApi.updateSystemSettings(data);

    if (response.code === 200) {
      ElMessage.success('保存基本设置成功');
      getSystemSettings();
    } else {
      ElMessage.error(response.msg || '保存基本设置失败');
    }
  } catch (error) {
    console.error('保存基本设置失败:', error);
    ElMessage.error('保存基本设置失败');
  } finally {
    basicSubmitting.value = false;
  }
}

// 保存支付设置
async function savePaymentSettings() {
  try {
    paymentSubmitting.value = true;

    // 构建请求数据
    const data = {
      wx_merchant_id: paymentForm.wxMerchantId || undefined,
      wx_merchant_key: paymentForm.wxMerchantKey || undefined,
      wx_app_id: paymentForm.wxAppId || undefined,
      wx_app_secret: paymentForm.wxAppSecret || undefined,
      wx_pay_enabled: paymentForm.wxPayEnabled,
      ali_merchant_id: paymentForm.aliMerchantId || undefined,
      ali_merchant_key: paymentForm.aliMerchantKey || undefined,
      ali_app_id: paymentForm.aliAppId || undefined,
      ali_pay_enabled: paymentForm.aliPayEnabled
    };

    const response = await systemSettingsApi.updatePaymentSettings(data);

    if (response.code === 200) {
      ElMessage.success('保存支付设置成功');
      getPaymentSettings();
    } else {
      ElMessage.error(response.msg || '保存支付设置失败');
    }
  } catch (error) {
    console.error('保存支付设置失败:', error);
    ElMessage.error('保存支付设置失败');
  } finally {
    paymentSubmitting.value = false;
  }
}

// 保存短信设置
async function saveSmsSettings() {
  try {
    smsSubmitting.value = true;

    // 构建请求数据
    const data = {
      sms_provider: smsForm.smsProvider,
      access_key: smsForm.accessKey || undefined,
      access_secret: smsForm.accessSecret || undefined,
      sign_name: smsForm.signName || undefined,
      verify_code_template: smsForm.verifyCodeTemplate || undefined,
      order_notify_template: smsForm.orderNotifyTemplate || undefined,
      sms_enabled: smsForm.smsEnabled
    };

    const response = await systemSettingsApi.updateSmsSettings(data);

    if (response.code === 200) {
      ElMessage.success('保存短信设置成功');
      getSmsSettings();
    } else {
      ElMessage.error(response.msg || '保存短信设置失败');
    }
  } catch (error) {
    console.error('保存短信设置失败:', error);
    ElMessage.error('保存短信设置失败');
  } finally {
    smsSubmitting.value = false;
  }
}

// 保存通知设置
async function saveNotificationSettings() {
  try {
    notificationSubmitting.value = true;

    // 构建请求数据
    const data = {
      order_create_template: notificationForm.orderCreateTemplate || undefined,
      order_pay_template: notificationForm.orderPayTemplate || undefined,
      service_complete_template: notificationForm.serviceCompleteTemplate || undefined,
      wx_template_enabled: notificationForm.wxTemplateEnabled,
      order_status_template: notificationForm.orderStatusTemplate || undefined,
      service_evaluation_template: notificationForm.serviceEvaluationTemplate || undefined,
      miniapp_subscribe_enabled: notificationForm.miniappSubscribeEnabled
    };

    const response = await systemSettingsApi.updateNotificationSettings(data);

    if (response.code === 200) {
      ElMessage.success('保存通知设置成功');
      getNotificationSettings();
    } else {
      ElMessage.error(response.msg || '保存通知设置失败');
    }
  } catch (error) {
    console.error('保存通知设置失败:', error);
    ElMessage.error('保存通知设置失败');
  } finally {
    notificationSubmitting.value = false;
  }
}

// 打开测试短信对话框
function openTestSmsDialog() {
  testSmsForm.phone = '';
  testSmsForm.type = 'verifyCode';
  testSmsDialogVisible.value = true;
}

// 发送测试短信
async function sendTestSms() {
  if (!testSmsForm.phone) {
    ElMessage.warning('请输入手机号');
    return;
  }

  try {
    testSmsLoading.value = true;

    // 构建请求数据
    const data = {
      phone: testSmsForm.phone,
      type: testSmsForm.type
    };

    const response = await systemSettingsApi.testSendSms(data);

    if (response.code === 200) {
      ElMessage.success('测试短信发送成功');
      testSmsDialogVisible.value = false;
    } else {
      ElMessage.error(response.msg || '测试短信发送失败');
    }
  } catch (error) {
    console.error('测试短信发送失败:', error);
    ElMessage.error('测试短信发送失败');
  } finally {
    testSmsLoading.value = false;
  }
}

// 监听选项卡切换
function handleTabChange(tab) {
  switch (tab) {
    case 'basic':
      if (!basicForm.systemName) {
        getSystemSettings();
      }
      break;
    case 'payment':
      if (!paymentForm.wxMerchantId && !paymentForm.aliMerchantId) {
        getPaymentSettings();
      }
      break;
    case 'sms':
      if (!smsForm.accessKey) {
        getSmsSettings();
      }
      break;
    case 'notification':
      if (!notificationForm.orderCreateTemplate) {
        getNotificationSettings();
      }
      break;
  }
}

// 页面加载时获取数据
onMounted(() => {
  getSystemSettings();

  // 监听选项卡切换
  watch(activeTab, handleTabChange);
});
</script>

<style scoped>
/* 这里是样式代码 */
</style>
