<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="mx-auto max-w-7xl">
      <!-- 顶部按钮组 -->
      <div class="mb-6 flex gap-3">
        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showCreateContractDialog">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>新建线索
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap">
          <el-icon class="mr-1">
            <Upload />
          </el-icon>批量导入客户线索
        </el-button>
        <el-button class="!rounded-button whitespace-nowrap">
          <el-icon class="mr-1">
            <Document />
          </el-icon>导入记录
        </el-button>
      </div>
      <!-- 搜索区域 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 flex flex-wrap gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">搜索客户</span>
            <el-select v-model="searchType" class="!rounded-button w-24">
              <el-option label="全部" value="all" />
              <el-option label="姓名" value="name" />
              <el-option label="电话" value="phone" />
            </el-select>
            <el-input v-model="searchForm.customerInfo" placeholder="请输入姓名、电话等关键字" class="w-64" />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">所属门店</span>
            <el-select v-model="searchForm.store" placeholder="厦门小羽佳缘索店" class="!rounded-button w-48">
              <el-option label="厦门小羽佳缘索店" value="store1" />
              <el-option label="厦门湖里店" value="store2" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">销售归属</span>
            <el-select v-model="searchForm.salesperson" placeholder="不限" class="!rounded-button w-32">
              <el-option label="不限" value="" />
              <el-option v-for="user in userList" :key="user.uuid" :label="`${user.name}(${user.role_name}${user.status_name ? `-${user.status_name}` : ''})`" :value="user.uuid" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">售后归属</span>
            <el-select v-model="searchForm.afterSalesperson" placeholder="不限" class="!rounded-button w-32">
              <el-option label="不限" value="" />
              <el-option v-for="user in userList" :key="user.uuid" :label="`${user.name}(${user.role_name}${user.status_name ? `-${user.status_name}` : ''})`" :value="user.uuid" />
            </el-select>
          </div>
        </div>
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">下次跟进时间</span>
            <div class="flex gap-2">
              <div class="flex flex-wrap gap-2">
                <el-button
                  v-for="(btn, index) in timeBtns"
                  :key="index"
                  :type="selectedTimeBtn === btn.value ? 'primary' : ''"
                  class="!rounded-button"
                  @click="handleFollowTimeClick(index, btn)"
                >
                  {{ btn.label }}
                </el-button>
              </div>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="起始日期"
                end-placeholder="结束日期"
                class="w-80"
              />
            </div>
          </div>
          <div v-show="showFilter" class="w-full">
            <!-- 录入时间 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">录入时间:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(btn, index) in timeBtns"
                    :key="index"
                    :type="selectedCreateTimeBtn === btn.value ? 'primary' : ''"

                    class="!rounded-button"
                    @click="handleCreateTimeClick(index, btn)"
                  >
                    {{ btn.label }}
                  </el-button>
                </div>
                <el-date-picker
                  v-model="createTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始日期"
                  end-placeholder="结束日期"
                  class="w-80"
                />
              </div>
            </div>
            <!-- 更新时间 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">更新时间:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(btn, index) in timeBtns"
                    :key="index"
                    :type="selectedUpdateTimeBtn === btn.value ? 'primary' : ''"

                    class="!rounded-button"
                    @click="handleUpdateTimeClick(index, btn)"
                  >
                    {{ btn.label }}
                  </el-button>
                </div>
                <el-date-picker
                  v-model="updateTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始日期"
                  end-placeholder="结束日期"
                  class="w-80"
                />
              </div>
            </div>
            <!-- 类型 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">合同类型:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(item, index) in customFields.demand"
                    :key="index"
                    :type="selectedTypeIndex === index ? 'primary' : ''"

                    class="!rounded-button"
                    @click="selectedTypeIndex = index; selectedType = typeof item === 'string' ? item : item.value || item"
                  >
                    {{ typeof item === 'string' ? item : item.name }}
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 来源 -->
            <div class="mb-4 flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <span class="w-20 text-gray-600">合同来源:</span>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="(item, index) in customFields.source"
                    :key="index"
                    :type="selectedSourceIndex === index ? 'primary' : ''"

                    class="!rounded-button"
                    @click="selectedSourceIndex = index; selectedSource = item.id"
                  >
                    {{ item.name }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ml-auto mt-4 flex items-center gap-3">
          <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
            <el-icon class="mr-1">
              <Search />
            </el-icon>查询
          </el-button>
          <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
            <el-icon class="mr-1">
              <Refresh />
            </el-icon>重置
          </el-button>
          <div class="flex items-center gap-4">
            <el-button class="!rounded-button whitespace-nowrap" @click="toggleFilter">
              {{ showFilter ? '收起' : '更多筛选条件' }}
              <el-icon class="ml-1">
                <component :is="showFilter ? ArrowUp : ArrowDown" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
      <!-- 状态标签 -->
      <div class="mb-4">
        <el-tabs v-model="activeTab" class="contract-tabs" @tab-change="handleTabChange">
          <el-tab-pane v-for="item in contractFilterList" :key="item.buried_point_tag" :label="`${item.name}(${item.amount})`" :name="item.buried_point_tag" />
        </el-tabs>
      </div>
      <!-- 表格区域 -->
      <div class="rounded-lg bg-white shadow-sm">
        <el-table :data="tableData" style="width: 100%;">
          <el-table-column prop="type" label="合同类型" />
          <el-table-column prop="service_time" label="起始日期" sortable />
          <el-table-column prop="service_end_time" label="结束日期" sortable />
          <el-table-column prop="status" label="合同状态">
            <template #default="{ row }">
              <span class="text-blue-500">{{ row.status_name }}</span>
              <div class="text-sm text-gray-400">
                {{ row.custom_demand_status_format }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="customer" label="客户">
            <template #default="{ row }">
              <div>{{ row.custom_name }}</div>
              <div class="text-sm text-gray-400">
                {{ row.custom_mobile }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="staff" label="签约家政员" width="220">
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <el-avatar :size="40" :src="row.aunt_avatar" />
                <div>
                  <div>{{ row.service_name }}</div>
                  <div class="text-sm text-gray-400">
                    工资{{ row.salary }}元
                  </div>
                  <div class="text-sm text-gray-400">
                    客户服务费{{ row.agency_fee }}元
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="electronic" label="电子合同">
            <template #default="{ row }">
              <el-tag :type="row.sign_contract_online === '1' ? 'success' : 'danger'" effect="plain">
                {{ row.sign_contract_online === '1' ? '已签约' : '未签约' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="user_name" label="销售归属" />
          <el-table-column prop="create_time" label="合同录入时间" sortable />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <div class="space-y-1">
                <el-button type="primary" link class="whitespace-nowrap" @click="viewContractDetail(scope.row)">
                  合同详情
                </el-button>
                <el-button type="primary" link class="whitespace-nowrap" @click="viewChangeRecords(scope.row)">
                  换人记录
                </el-button>
                <el-button v-if="scope.row.status === 1" type="warning" link class="whitespace-nowrap" @click="showChangeStatusDialog(scope.row)">
                  变更状态
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-end px-6 py-4">
          <el-pagination
            v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- 合同详情对话框 -->
  <el-dialog v-model="detailDialogVisible" title="合同详情" width="800px">
    <div v-if="selectedContract" class="p-4">
      <div class="mb-6">
        <h3 class="mb-2 text-lg font-semibold">
          基本信息
        </h3>
        <div class="grid grid-cols-2 mb-4 gap-4">
          <div>
            <span class="text-gray-600">合同编号：</span>
            <span>{{ selectedContract.contract_number }}</span>
          </div>
          <div>
            <span class="text-gray-600">合同类型：</span>
            <span>{{ selectedContract.type }}</span>
          </div>
          <div>
            <span class="text-gray-600">合同状态：</span>
            <span>{{ selectedContract.status_name }}</span>
          </div>
          <div>
            <span class="text-gray-600">签约时间：</span>
            <span>{{ selectedContract.sign_time }}</span>
          </div>
          <div>
            <span class="text-gray-600">开始时间：</span>
            <span>{{ selectedContract.service_time }}</span>
          </div>
          <div>
            <span class="text-gray-600">结束时间：</span>
            <span>{{ selectedContract.service_end_time }}</span>
          </div>
          <div>
            <span class="text-gray-600">客户姓名：</span>
            <span>{{ selectedContract.custom_name }}</span>
          </div>
          <div>
            <span class="text-gray-600">客户手机：</span>
            <span>{{ selectedContract.custom_mobile }}</span>
          </div>
          <div>
            <span class="text-gray-600">家政员：</span>
            <span>{{ selectedContract.service_name }}</span>
          </div>
          <div>
            <span class="text-gray-600">工资：</span>
            <span>{{ selectedContract.salary }} 元</span>
          </div>
          <div>
            <span class="text-gray-600">客户服务费：</span>
            <span>{{ selectedContract.agency_fee }} 元</span>
          </div>
          <div>
            <span class="text-gray-600">电子合同：</span>
            <span>{{ selectedContract.sign_contract_online === '1' ? '已签约' : '未签约' }}</span>
          </div>
        </div>
      </div>

      <div v-if="contractFiles.length > 0" class="mb-6">
        <h3 class="mb-2 text-lg font-semibold">
          合同附件
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="(file, index) in contractFiles" :key="index" class="border rounded p-3">
            <div class="flex items-center justify-between">
              <div class="truncate">
                <el-icon class="mr-1">
                  <Document />
                </el-icon>
                {{ file.name }}
              </div>
              <el-button type="primary" link @click="downloadFile(file.url)">
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="contractLogs.length > 0" class="mb-6">
        <h3 class="mb-2 text-lg font-semibold">
          操作日志
        </h3>
        <el-timeline>
          <el-timeline-item
            v-for="(log, index) in contractLogs"
            :key="index"
            :timestamp="log.create_time"
          >
            {{ log.content }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 星级评分部分 -->
      <div class="mb-6">
        <h3 class="mb-2 text-lg font-semibold">
          服务评价
        </h3>
        <div v-if="evaluationStats.totalCount > 0" class="mb-4 rounded-lg bg-gray-50 p-4">
          <div class="flex items-center gap-8">
            <div class="text-center">
              <div class="mb-2 text-3xl text-primary font-bold">
                {{ evaluationStats.avgRating.toFixed(1) }}
              </div>
              <div class="text-sm text-gray-500">
                综合评分
              </div>
            </div>
            <div class="flex-1">
              <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="mb-1 flex items-center gap-2">
                <span class="w-8 text-sm text-gray-500">{{ rating }}星</span>
                <el-progress
                  :percentage="Math.round((evaluationStats.ratingDistribution[rating] / evaluationStats.totalCount) * 100)"
                  :show-text="false"
                  class="flex-1"
                />
                <span class="w-12 text-sm text-gray-500">{{ evaluationStats.ratingDistribution[rating] }}条</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="py-4 text-center text-gray-500">
          暂无评价数据
        </div>

        <!-- 评价列表 -->
        <div v-if="evaluationList.length > 0" class="mt-4">
          <div v-for="(evaluation, index) in evaluationList" :key="index" class="border-b py-4 last:border-none">
            <div class="flex items-start gap-4">
              <el-avatar :size="40" :src="evaluation.avatar || ''" />
              <div class="flex-1">
                <div class="mb-2 flex items-center gap-2">
                  <span class="font-medium">{{ evaluation.customer_name || '匿名用户' }}</span>
                  <el-rate v-model="evaluation.rating" disabled text-color="#ff9900" />
                  <span class="text-sm text-gray-400">{{ evaluation.create_time }}</span>
                </div>
                <div class="mb-2 text-gray-700">
                  {{ evaluation.content }}
                </div>
                <div v-if="evaluation.images && evaluation.images.length" class="mb-2 flex gap-2">
                  <el-image
                    v-for="(image, imgIndex) in evaluation.images"
                    :key="imgIndex"
                    :src="image"
                    :preview-src-list="evaluation.images"
                    fit="cover"
                    class="h-20 w-20 rounded"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加评价按钮 -->
        <div class="mt-4 flex justify-center">
          <el-button type="primary" @click="showEvaluationDialog = true">
            添加评价
          </el-button>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 换人记录对话框 -->
  <el-dialog v-model="changeRecordsDialogVisible" title="换人记录" width="700px">
    <div v-if="changeRecords.length > 0" class="p-4">
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in changeRecords"
          :key="index"
          :timestamp="record.create_time"
        >
          <div class="mb-2">
            <span class="font-semibold">原家政员：</span>
            {{ record.old_service_name }}
          </div>
          <div class="mb-2">
            <span class="font-semibold">新家政员：</span>
            {{ record.new_service_name }}
          </div>
          <div>
            <span class="font-semibold">更换原因：</span>
            {{ record.reason }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <div v-else class="p-4 text-center text-gray-500">
      暂无换人记录
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="changeRecordsDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 添加评价对话框 -->
  <el-dialog v-model="showEvaluationDialog" title="添加服务评价" width="600px">
    <div class="p-4">
      <el-form ref="evaluationFormRef" :model="evaluationForm" label-width="80px" :rules="evaluationRules">
        <el-form-item label="综合评分" prop="rating">
          <el-rate v-model="evaluationForm.rating" :colors="['#FFCC33', '#FFCC33', '#FFCC33']" />
        </el-form-item>
        <el-form-item label="服务态度" prop="attitude">
          <el-rate v-model="evaluationForm.attitude" :colors="['#FFCC33', '#FFCC33', '#FFCC33']" />
        </el-form-item>
        <el-form-item label="专业程度" prop="professional">
          <el-rate v-model="evaluationForm.professional" :colors="['#FFCC33', '#FFCC33', '#FFCC33']" />
        </el-form-item>
        <el-form-item label="责任心" prop="responsibility">
          <el-rate v-model="evaluationForm.responsibility" :colors="['#FFCC33', '#FFCC33', '#FFCC33']" />
        </el-form-item>
        <el-form-item label="评价内容" prop="content">
          <el-input
            v-model="evaluationForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入您对服务的评价"
          />
        </el-form-item>
        <el-form-item label="上传图片">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="5"
            :on-change="handleEvaluationImageChange"
            :on-remove="handleEvaluationImageRemove"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="匿名评价">
          <el-switch v-model="evaluationForm.anonymous" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showEvaluationDialog = false">取消</el-button>
        <el-button type="primary" @click="submitEvaluation">提交评价</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 创建合同对话框 -->
  <el-dialog v-model="createContractDialogVisible" title="创建合同" width="800px">
    <div class="p-4">
      <el-form ref="contractFormRef" :model="contractForm" label-width="100px" :rules="contractRules">
        <el-form-item label="合同类型" prop="type">
          <el-select v-model="contractForm.type" placeholder="请选择合同类型" class="w-full">
            <el-option v-for="item in contractTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户" prop="customer_uuid">
          <el-select v-model="contractForm.customer_uuid" placeholder="请选择客户" class="w-full">
            <el-option v-for="item in customerList" :key="item.uuid" :label="item.name" :value="item.uuid" />
          </el-select>
        </el-form-item>
        <el-form-item label="家政员" prop="service_uuid">
          <el-select v-model="contractForm.service_uuid" placeholder="请选择家政员" class="w-full">
            <el-option v-for="item in serviceList" :key="item.uuid" :label="item.name" :value="item.uuid" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="service_time">
          <el-date-picker v-model="contractForm.service_time" type="date" placeholder="选择开始日期" class="w-full" />
        </el-form-item>
        <el-form-item label="结束日期" prop="service_end_time">
          <el-date-picker v-model="contractForm.service_end_time" type="date" placeholder="选择结束日期" class="w-full" />
        </el-form-item>
        <el-form-item label="工资" prop="salary">
          <el-input-number v-model="contractForm.salary" :min="0" :precision="2" :step="100" class="w-full" />
        </el-form-item>
        <el-form-item label="客户服务费" prop="agency_fee">
          <el-input-number v-model="contractForm.agency_fee" :min="0" :precision="2" :step="100" class="w-full" />
        </el-form-item>
        <el-form-item label="电子合同" prop="sign_contract_online">
          <el-switch v-model="contractForm.sign_contract_online" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="contractForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="createContractDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitContract">提交</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 变更合同状态对话框 -->
  <el-dialog v-model="changeStatusDialogVisible" title="变更合同状态" width="500px">
    <div class="p-4">
      <el-form ref="statusFormRef" :model="statusForm" label-width="80px" :rules="statusRules">
        <el-form-item label="新状态" prop="status">
          <el-select v-model="statusForm.status" placeholder="请选择新状态" class="w-full">
            <el-option label="有效" :value="1" />
            <el-option label="已过期" :value="2" />
            <el-option label="已终止" :value="3" />
            <el-option label="待生效" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="reason">
          <el-input v-model="statusForm.reason" type="textarea" :rows="3" placeholder="请输入变更原因" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="changeStatusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitStatusChange">确认变更</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import storeApi from '@/api/modules/housekeeping/service_contract'
import { ArrowDown, ArrowUp, Document, Plus, Refresh, Search, Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'

const searchType = ref('all')
const searchForm = ref({
  customerInfo: '',
  store: '',
  salesperson: '',
  electronic: '',
  afterSalesperson: '',
})

// 时间相关变量
const dateRange = ref([])
const createTimeRange = ref([])
const updateTimeRange = ref([])
const selectedTimeBtn = ref('unlimited')
const selectedCreateTimeBtn = ref('unlimited')
const selectedUpdateTimeBtn = ref('unlimited')

// 筛选条件选择数据
const selectedType = ref('')
const selectedTag = ref('')
const selectedSource = ref('')
const selectedTypeIndex = ref(-1)
const selectedTagIndex = ref(-1)
const selectedSourceIndex = ref(-1)

// 时间按钮选项
const timeBtns = [
  { label: '不限', value: 'unlimited' },
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '明日', value: 'tomorrow' },
  { label: '后天', value: 'afterTomorrow' },
  { label: '本周', value: 'thisWeek' },
]
const activeTab = ref('all')
const contractFilterList = ref([])
const tableData = ref([])
function handleSearch() {
  // 重置页码并获取数据
  currentPage.value = 1
  getStoreCustomContractList()
}

function handleReset() {
  // 重置搜索表单
  searchForm.value = {
    customerInfo: '',
    store: '',
    salesperson: '',
    electronic: '',
    afterSalesperson: '',
  }

  // 重置时间相关变量
  dateRange.value = []
  createTimeRange.value = []
  updateTimeRange.value = []
  selectedTimeBtn.value = 'unlimited'
  selectedCreateTimeBtn.value = 'unlimited'
  selectedUpdateTimeBtn.value = 'unlimited'

  // 重置筛选条件
  selectedType.value = ''
  selectedTag.value = ''
  selectedSource.value = ''
  selectedTypeIndex.value = -1
  selectedTagIndex.value = -1
  selectedSourceIndex.value = -1

  // 重置页码并获取数据
  currentPage.value = 1
  getStoreCustomContractList()
}

// 处理下次跟进时间按钮点击
function handleFollowTimeClick(index: number, btn: any) {
  selectedTimeBtn.value = btn.value
  // 清空日期范围
  dateRange.value = []
}

// 处理录入时间按钮点击
function handleCreateTimeClick(index: number, btn: any) {
  selectedCreateTimeBtn.value = btn.value
  // 清空日期范围
  createTimeRange.value = []
}

// 处理更新时间按钮点击
function handleUpdateTimeClick(index: number, btn: any) {
  selectedUpdateTimeBtn.value = btn.value
  // 清空日期范围
  updateTimeRange.value = []
}
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)
function handleSizeChange(val: number) {
  pageSize.value = val
  getStoreCustomContractList()
}

function handleCurrentChange(val: number) {
  currentPage.value = val
  getStoreCustomContractList()
}
// 门店和用户列表数据
const storeList = ref([])
const userList = ref([])

// 定义类型
interface Store {
  name: string
  uuid: string
}

interface User {
  uuid: string
  name: string
  role_name: string
  status: string
}

// 获取门店和用户列表
async function getStoreAndUserList() {
  try {
    const params = {
      scene: 'contract', // 使用合同场景
    }
    const res = await storeApi.getStoreAndUserList(params)
    console.log('获取门店和用户列表成功:', res)
    if (res && res.data) {
      // 处理门店数据
      storeList.value = res.data.stores?.map((store: Store) => ({
        label: store.name,
        value: store.uuid,
      })) || []

      // 处理用户数据
      userList.value = res.data.users?.map((user: User) => ({
        uuid: user.uuid,
        name: user.name,
        role_name: user.role_name,
        status_name: user.status || '',
      })) || []
    }
  }
  catch (error) {
    console.error('获取门店和用户列表失败:', error)
    ElMessage.error('获取门店和用户列表失败')
  }
}

// 添加筛选条件显示状态
const showFilter = ref(false)
function toggleFilter() {
  showFilter.value = !showFilter.value
}

// 添加自定义字段数据
const customFields = ref({
  demand: [],
  tag: [],
  source: [],
  salary_unit: [],
})

// 合同详情相关
const detailDialogVisible = ref(false)
const selectedContract = ref(null)
const contractFiles = ref([])
const contractLogs = ref([])

// 评价相关数据
const evaluationList = ref<any[]>([])
const evaluationStats = ref({
  avgRating: 0,
  totalCount: 0,
  ratingDistribution: {
    5: 0,
    4: 0,
    3: 0,
    2: 0,
    1: 0,
  } as Record<number, number>,
})
const showEvaluationDialog = ref(false)
const evaluationForm = ref({
  rating: 5,
  attitude: 5,
  professional: 5,
  responsibility: 5,
  content: '',
  images: [] as string[],
  anonymous: false,
})
const evaluationRules = {
  rating: [{ required: true, message: '请选择评分', trigger: 'change' }],
  content: [{ required: true, message: '请输入评价内容', trigger: 'blur' }],
}
const evaluationFormRef = ref(null)

// 创建合同相关数据
const createContractDialogVisible = ref(false)
const contractForm = ref({
  type: '',
  customer_uuid: '',
  service_uuid: '',
  service_time: '',
  service_end_time: '',
  salary: 0,
  agency_fee: 0,
  sign_contract_online: 0,
  remark: '',
})
const contractRules = {
  type: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  customer_uuid: [{ required: true, message: '请选择客户', trigger: 'change' }],
  service_uuid: [{ required: true, message: '请选择家政员', trigger: 'change' }],
  service_time: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  service_end_time: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  salary: [{ required: true, message: '请输入工资', trigger: 'blur' }],
  agency_fee: [{ required: true, message: '请输入客户服务费', trigger: 'blur' }],
}
const contractFormRef = ref(null)
const contractTypes = ref([
  { label: '长期工', value: 'long_term' },
  { label: '钟点工', value: 'hourly' },
  { label: '临时工', value: 'temporary' },
])
const customerList = ref<any[]>([])
const serviceList = ref<any[]>([])

// 变更合同状态相关数据
const changeStatusDialogVisible = ref(false)
const statusForm = ref({
  status: 1,
  reason: '',
})
const statusRules = {
  status: [{ required: true, message: '请选择新状态', trigger: 'change' }],
  reason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
}
const statusFormRef = ref(null)
const currentContractUuid = ref('') // 当前操作的合同UUID

// 换人记录相关
const changeRecordsDialogVisible = ref(false)
const changeRecords = ref([])

async function getCustomFields() {
  try {
    const res = await storeApi.getContractFields()
    console.log('获取合同字段成功:', res)
    if (res && res.data) {
      // 根据接口返回的数据结构进行处理
      customFields.value = {
        demand: res.data.demand_types || [],
        tag: res.data.tags || [],
        source: res.data.sources || [],
        salary_unit: res.data.salary_units || [],
      }
    }
  }
  catch (error) {
    console.error('获取合同字段失败:', error)
    ElMessage.error('获取合同字段失败')
  }
}

async function getStoreCustomContractList() {
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      status: activeTab.value === 'all' ? null : getStatusByTab(activeTab.value),
      store_uuid: searchForm.value.store || undefined,
      user_uuid: searchForm.value.salesperson || undefined,
      sign_contract_online: searchForm.value.electronic || undefined,
      keyword: searchForm.value.customerInfo || undefined,
    }

    // 调用API获取合同列表
    const response = await storeApi.getStoreCustomerContractList(params)

    if (response && response.data) {
      // 更新合同筛选列表
      contractFilterList.value = response.data.filter_list || []

      // 更新合同列表
      tableData.value = response.data.contracts || []

      // 更新总数
      total.value = response.data.total || 0
    }
    else {
      ElMessage.error('获取合同列表失败')
      contractFilterList.value = []
      tableData.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取合同列表失败:', error)
    ElMessage.error('获取合同列表失败')
    contractFilterList.value = []
    tableData.value = []
    total.value = 0
  }
}

// 标签页切换处理
function handleTabChange(tab: string | number) {
  activeTab.value = tab as string
  currentPage.value = 1 // 重置页码
  getStoreCustomContractList()
}

// 根据标签页获取状态值
function getStatusByTab(tab: string) {
  switch (tab) {
    case 'active': return 1 // 有效
    case 'expired': return 2 // 已过期
    case 'terminated': return 3 // 已终止
    case 'pending': return 4 // 待生效
    default: return null // 全部
  }
}

// 查看合同详情
async function viewContractDetail(contract: any) {
  try {
    selectedContract.value = contract
    detailDialogVisible.value = true

    // 获取合同详情
    if (contract && contract.uuid) {
      const response = await storeApi.getStoreCustomerContractDetail(contract.uuid)

      if (response && response.data) {
        // 更新合同详情
        selectedContract.value = {
          ...(selectedContract.value || {}),
          ...response.data,
        }

        // 获取合同档案
        getContractArchives(contract.uuid)

        // 获取合同操作日志
        getContractLogs(contract.uuid)

        // 获取合同评价数据
        getContractEvaluations(contract.uuid)
      }
      else {
        ElMessage.error('获取合同详情失败')
      }
    }
  }
  catch (error) {
    console.error('获取合同详情失败:', error)
    ElMessage.error('获取合同详情失败')
  }
}

// 获取合同档案
async function getContractArchives(contractUuid: string) {
  try {
    const response = await storeApi.getContractArchiveList(contractUuid)

    if (response && response.data) {
      contractFiles.value = response.data.files?.map((file: any) => ({
        name: file.name || '合同文件',
        url: file.url || '',
      })) || []
    }
    else {
      contractFiles.value = []
    }
  }
  catch (error) {
    console.error('获取合同档案失败:', error)
    contractFiles.value = []
  }
}

// 获取合同操作日志
async function getContractLogs(contractUuid: string) {
  try {
    const response = await storeApi.getContractOperationLog(contractUuid)

    if (response && response.data) {
      contractLogs.value = response.data.logs || []
    }
    else {
      contractLogs.value = []
    }
  }
  catch (error) {
    console.error('获取合同操作日志失败:', error)
    contractLogs.value = []
  }
}

// 查看换人记录
async function viewChangeRecords(contract: any) {
  try {
    changeRecordsDialogVisible.value = true

    // 获取换人记录
    if (contract && contract.uuid) {
      const response = await storeApi.getContractChangeRecords(contract.uuid)

      if (response && response.data) {
        changeRecords.value = response.data.records || []
      }
      else {
        changeRecords.value = []
      }
    }
  }
  catch (error) {
    console.error('获取换人记录失败:', error)
    ElMessage.error('获取换人记录失败')
    changeRecords.value = []
  }
}

// 下载文件
function downloadFile(url: string) {
  if (!url) {
    ElMessage.warning('文件链接无效')
    return
  }

  // 在新标签页中打开链接
  window.open(url, '_blank')
}

// 获取合同评价数据
async function getContractEvaluations(contractUuid: string) {
  try {
    console.log('获取合同评价数据:', contractUuid)
    // 模拟评价数据，实际项目中应该调用API获取
    // 例如：const response = await evaluationApi.getContractEvaluations(contractUuid)

    // 模拟评价统计数据
    evaluationStats.value = {
      avgRating: 4.5,
      totalCount: 10,
      ratingDistribution: {
        5: 6,
        4: 2,
        3: 1,
        2: 1,
        1: 0,
      } as Record<number, number>,
    }

    // 模拟评价列表数据
    evaluationList.value = [
      {
        id: '1',
        customer_name: '张三',
        avatar: '',
        rating: 5,
        content: '服务非常好，家政员很专业，下次还会选择。',
        create_time: '2023-05-15 14:30',
        images: [],
      },
      {
        id: '2',
        customer_name: '李四',
        avatar: '',
        rating: 4,
        content: '整体满意，但是有些小细节可以做得更好。',
        create_time: '2023-05-10 09:15',
        images: [],
      },
    ]
  }
  catch (error) {
    console.error('获取合同评价数据失败:', error)
    ElMessage.error('获取合同评价数据失败')
    evaluationStats.value = {
      avgRating: 0,
      totalCount: 0,
      ratingDistribution: {
        5: 0,
        4: 0,
        3: 0,
        2: 0,
        1: 0,
      },
    }
    evaluationList.value = []
  }
}

// 处理评价图片上传
function handleEvaluationImageChange(file: any) {
  // 处理图片上传逻辑
  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    if (e.target && e.target.result) {
      const result = e.target.result as string
      evaluationForm.value.images.push(result)
    }
  }
  reader.readAsDataURL(file.raw)
  return false // 阻止自动上传
}

// 处理评价图片移除
function handleEvaluationImageRemove(file: any, _fileList: any) {
  const index = evaluationForm.value.images.indexOf(file.url)
  if (index !== -1) {
    evaluationForm.value.images.splice(index, 1)
  }
}

// 提交评价
async function submitEvaluation() {
  try {
    // 模拟提交评价，实际项目中应该调用API
    // 例如：await evaluationApi.submitContractEvaluation({
    //   contract_uuid: selectedContract.value.uuid,
    //   rating: evaluationForm.value.rating,
    //   attitude: evaluationForm.value.attitude,
    //   professional: evaluationForm.value.professional,
    //   responsibility: evaluationForm.value.responsibility,
    //   content: evaluationForm.value.content,
    //   images: evaluationForm.value.images,
    //   anonymous: evaluationForm.value.anonymous
    // })

    // 添加到评价列表
    const newEvaluation = {
      id: Date.now().toString(),
      customer_name: evaluationForm.value.anonymous ? '匿名用户' : '当前用户',
      avatar: '',
      rating: evaluationForm.value.rating,
      content: evaluationForm.value.content,
      create_time: new Date().toLocaleString(),
      images: [...evaluationForm.value.images],
    }

    evaluationList.value.unshift(newEvaluation)

    // 更新评价统计数据
    evaluationStats.value.totalCount++
    evaluationStats.value.ratingDistribution[evaluationForm.value.rating]++

    // 重新计算平均分
    let totalScore = 0
    let totalCount = 0
    for (let i = 1; i <= 5; i++) {
      totalScore += i * evaluationStats.value.ratingDistribution[i]
      totalCount += evaluationStats.value.ratingDistribution[i]
    }
    evaluationStats.value.avgRating = totalCount > 0 ? totalScore / totalCount : 0

    // 关闭对话框并重置表单
    showEvaluationDialog.value = false
    evaluationForm.value = {
      rating: 5,
      attitude: 5,
      professional: 5,
      responsibility: 5,
      content: '',
      images: [],
      anonymous: false,
    }

    ElMessage.success('评价提交成功')
  }
  catch (error) {
    console.error('提交评价失败:', error)
    ElMessage.error('提交评价失败')
  }
}

// 显示创建合同对话框
function showCreateContractDialog() {
  createContractDialogVisible.value = true
  // 重置表单
  contractForm.value = {
    type: '',
    customer_uuid: '',
    service_uuid: '',
    service_time: '',
    service_end_time: '',
    salary: 0,
    agency_fee: 0,
    sign_contract_online: 0,
    remark: '',
  }
  // 获取客户列表和家政员列表
  getCustomerList()
  getServiceList()
}

// 获取客户列表
async function getCustomerList() {
  try {
    // 这里应该调用获取客户列表的API
    // 模拟数据
    customerList.value = [
      { uuid: 'customer1', name: '张三' },
      { uuid: 'customer2', name: '李四' },
      { uuid: 'customer3', name: '王五' },
    ]
  }
  catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
  }
}

// 获取家政员列表
async function getServiceList() {
  try {
    // 这里应该调用获取家政员列表的API
    // 模拟数据
    serviceList.value = [
      { uuid: 'service1', name: '赵阿姨' },
      { uuid: 'service2', name: '钱阿姨' },
      { uuid: 'service3', name: '孙阿姨' },
    ]
  }
  catch (error) {
    console.error('获取家政员列表失败:', error)
    ElMessage.error('获取家政员列表失败')
  }
}

// 提交合同
async function submitContract() {
  try {
    // 表单验证
    if (!contractFormRef.value)
      return

    // 由于类型问题，我们使用try-catch来处理可能的验证错误
    try {
      // @ts-expect-error - contractFormRef.value 可能为 null 或 undefined，但我们已经在上面检查过了
      await contractFormRef.value.validate()
    }
    catch (validationError) {
      console.error('表单验证失败:', validationError)
      ElMessage.error('表单验证失败，请检查输入')
      return
    }

    // 提交合同数据
    const response = await storeApi.createContract(contractForm.value)

    if (response && response.data) {
      ElMessage.success('创建合同成功')
      createContractDialogVisible.value = false
      // 刷新合同列表
      getStoreCustomContractList()
    }
    else {
      ElMessage.error('创建合同失败')
    }
  }
  catch (error) {
    console.error('创建合同失败:', error)
    ElMessage.error('创建合同失败')
  }
}

// 显示变更合同状态对话框
function showChangeStatusDialog(contract: any) {
  changeStatusDialogVisible.value = true
  currentContractUuid.value = contract.uuid
  statusForm.value = {
    status: contract.status || 1,
    reason: '',
  }
}

// 提交状态变更
async function submitStatusChange() {
  try {
    // 表单验证
    if (!statusFormRef.value)
      return

    // 由于类型问题，我们使用try-catch来处理可能的验证错误
    try {
      // @ts-expect-error - statusFormRef.value 可能为 null 或 undefined，但我们已经在上面检查过了
      await statusFormRef.value.validate()
    }
    catch (validationError) {
      console.error('表单验证失败:', validationError)
      ElMessage.error('表单验证失败，请检查输入')
      return
    }

    // 提交状态变更
    const response = await storeApi.changeContractStatus(currentContractUuid.value, statusForm.value)

    if (response && response.data) {
      ElMessage.success('变更合同状态成功')
      changeStatusDialogVisible.value = false
      // 刷新合同列表
      getStoreCustomContractList()
    }
    else {
      ElMessage.error('变更合同状态失败')
    }
  }
  catch (error) {
    console.error('变更合同状态失败:', error)
    ElMessage.error('变更合同状态失败')
  }
}

// 添加合同操作按钮
// 注释掉未使用的函数
/*
function addContractOperations(row: any) {
  const operations = [
    {
      name: '合同详情',
      handler: viewContractDetail,
    },
    {
      name: '换人记录',
      handler: viewChangeRecords,
    },
  ]

  // 根据合同状态添加不同的操作
  if (row.status === 1) { // 有效状态
    operations.push({
      name: '变更状态',
      handler: showChangeStatusDialog,
    })
  }

  return operations
}
*/

onMounted(() => {
  getStoreAndUserList()
  getCustomFields()
  getStoreCustomContractList()
})
</script>

<style scoped>
.contract-tabs :deep(.el-tabs__nav) {
  border: none;
}

.contract-tabs :deep(.el-tabs__item) {
  padding: 0 16px;
  font-size: 14px;
}

.contract-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 500;
  color: #1890ff;
}

.contract-tabs :deep(.el-tabs__active-bar) {
  background-color: #1890ff;
}
</style>
