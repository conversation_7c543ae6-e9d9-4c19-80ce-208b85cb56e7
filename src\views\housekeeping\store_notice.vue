<template>
  <div class="store-notice-container">
    <!-- 公告列表 -->
    <div class="notice-list">
      <div class="list-header">
        <h2 class="list-title">店铺公告列表</h2>
        <el-button type="primary" @click="showAddForm">添加公告</el-button>
      </div>

      <el-table v-loading="loading" :data="noticeList" style="width: 100%" border>
        <el-table-column prop="title" label="公告标题" min-width="150" />
        <el-table-column prop="content" label="公告内容" min-width="250" show-overflow-tooltip />
        <el-table-column prop="store_name" label="所属店铺" width="150" />
        <el-table-column prop="type_name" label="类型" width="100" />
        <el-table-column prop="status_name" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'published' ? 'success' : 'info'">
              {{ row.status_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publish_time" label="发布时间" width="180" />
        <el-table-column prop="end_time" label="结束时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑公告表单 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑公告' : '添加公告'" width="600px">
      <div class="notice-form">
        <el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
          <el-form-item label="公告标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入公告标题" />
          </el-form-item>

          <el-form-item label="公告内容" prop="content">
            <el-input v-model="form.content" type="textarea" rows="6" placeholder="请输入公告内容" />
          </el-form-item>

          <el-form-item label="所属店铺" prop="store_uuid">
            <el-select v-model="form.store_uuid" placeholder="请选择店铺" class="w-full">
              <el-option v-for="store in storeList" :key="store.value" :label="store.label" :value="store.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="公告类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择类型" class="w-full">
              <el-option label="公告" value="notice" />
              <el-option label="活动" value="activity" />
              <el-option label="通知" value="notification" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态" class="w-full">
              <el-option label="已发布" value="published" />
              <el-option label="草稿" value="draft" />
            </el-select>
          </el-form-item>

          <el-form-item label="有效时间" required>
            <el-col :span="11">
              <el-form-item prop="publish_time">
                <el-date-picker
                  v-model="form.publish_time"
                  type="datetime"
                  placeholder="发布时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="text-gray-500">至</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="end_time">
                <el-date-picker
                  v-model="form.end_time"
                  type="datetime"
                  placeholder="结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除确认" width="400px">
      <div>确定要删除该公告吗？删除后无法恢复。</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">确定删除</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import storeNoticeApi from '@/api/modules/housekeeping/store_notice'

// 公告列表相关
const noticeList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框控制
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const deleteLoading = ref(false)
const selectedNotice = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const form = reactive({
  uuid: '',
  title: '',
  content: '',
  store_uuid: '',
  type: 'notice',
  status: 'published',
  publish_time: '',
  end_time: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度应在2-50个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  store_uuid: [
    { required: true, message: '请选择所属店铺', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  publish_time: [
    { required: true, message: '请选择发布时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 店铺列表
const storeList = ref([
  { label: '北京朝阳店', value: 'store123' },
  { label: '北京海淀店', value: 'store456' },
  { label: '北京丰台店', value: 'store789' }
])

// 获取公告列表
async function getNoticeList() {
  try {
    loading.value = true

    const params = {
      page: currentPage.value,
      size: pageSize.value
    }

    const response = await storeNoticeApi.getList(params)

    if (response.code === 200 && response.data) {
      noticeList.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取公告列表失败')
      noticeList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
    noticeList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页处理
function handleSizeChange(size) {
  pageSize.value = size
  currentPage.value = 1
  getNoticeList()
}

function handleCurrentChange(page) {
  currentPage.value = page
  getNoticeList()
}

// 显示添加表单
function showAddForm() {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 重置表单
function resetForm() {
  form.uuid = ''
  form.title = ''
  form.content = ''
  form.store_uuid = ''
  form.type = 'notice'
  form.status = 'published'
  form.publish_time = new Date()

  // 设置结束时间为一周后
  const endDate = new Date()
  endDate.setDate(endDate.getDate() + 7)
  form.end_time = endDate

  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 编辑公告
function handleEdit(notice) {
  isEdit.value = true
  selectedNotice.value = notice

  // 填充表单数据
  form.uuid = notice.uuid
  form.title = notice.title
  form.content = notice.content
  form.store_uuid = notice.store_uuid
  form.type = notice.type
  form.status = notice.status
  form.publish_time = notice.publish_time ? new Date(notice.publish_time) : new Date()
  form.end_time = notice.end_time ? new Date(notice.end_time) : new Date()

  dialogVisible.value = true
}

// 删除公告
function handleDelete(notice) {
  selectedNotice.value = notice
  deleteDialogVisible.value = true
}

// 确认删除
async function confirmDelete() {
  if (!selectedNotice.value || !selectedNotice.value.uuid) {
    ElMessage.warning('无法获取公告信息')
    return
  }

  try {
    deleteLoading.value = true

    const response = await storeNoticeApi.delete(selectedNotice.value.uuid)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      deleteDialogVisible.value = false
      getNoticeList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除公告失败:', error)
    ElMessage.error('删除失败，请重试')
  } finally {
    deleteLoading.value = false
  }
}

// 提交表单
async function submitForm() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 格式化时间
    const formatDateTime = (date) => {
      if (!date) return ''
      const d = new Date(date)
      return d.toISOString().replace('T', ' ').substring(0, 19)
    }

    // 构建请求数据
    const noticeData = {
      title: form.title,
      content: form.content,
      store_uuid: form.store_uuid,
      type: form.type,
      status: form.status,
      publish_time: formatDateTime(form.publish_time),
      end_time: formatDateTime(form.end_time)
    }

    // 如果是编辑模式，添加uuid
    if (isEdit.value && form.uuid) {
      noticeData.uuid = form.uuid
    }

    // 调用API
    const apiMethod = isEdit.value ? storeNoticeApi.update : storeNoticeApi.add
    const response = await apiMethod(noticeData)

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
      dialogVisible.value = false
      getNoticeList()
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('请检查表单内容')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取公告列表
onMounted(() => {
  getNoticeList()
})
</script>

<style scoped>
.store-notice-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.notice-list {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.notice-form {
  background-color: #fff;
  border-radius: 8px;
  padding: 0;
}

.w-full {
  width: 100%;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #909399;
}
</style>
