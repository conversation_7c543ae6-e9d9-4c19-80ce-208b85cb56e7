<template>
  <div class="company-settings">
    <el-card>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="formData.companyName" placeholder="请输入公司名称" />
        </el-form-item>

        <el-form-item label="公司logo" prop="logo">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="handleLogoChange"
          >
            <img v-if="formData.logo" :src="formData.logo" class="avatar">
            <el-icon v-else class="avatar-uploader-icon">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="公司地址" prop="address">
          <el-input v-model="formData.address" placeholder="请输入公司地址" />
        </el-form-item>

        <el-form-item label="营业时间" required>
          <el-form-item prop="businessHours.start" class="mb-0">
            <el-time-picker
              v-model="formData.businessHours.start"
              placeholder="开始时间"
              format="HH:mm"
            />
          </el-form-item>
          <span class="mx-2">至</span>
          <el-form-item prop="businessHours.end" class="mb-0">
            <el-time-picker
              v-model="formData.businessHours.end"
              placeholder="结束时间"
              format="HH:mm"
            />
          </el-form-item>
        </el-form-item>

        <el-form-item label="公司简介" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入公司简介"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="saveSettings">
            保存设置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = ref({
  companyName: '',
  logo: '',
  phone: '',
  address: '',
  businessHours: {
    start: '',
    end: '',
  },
  description: '',
})

function validatePhone(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入联系电话'))
  }
  else if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的手机号格式'))
  }
  else {
    callback()
  }
}

function validateBusinessHours(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error('请选择时间'))
  }
  else {
    callback()
  }
}

const rules: FormRules = {
  'companyName': [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  'logo': [{ required: true, message: '请上传公司logo', trigger: 'change' }],
  'phone': [{ validator: validatePhone, trigger: 'blur' }],
  'address': [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
  'businessHours.start': [{ validator: validateBusinessHours, trigger: 'change' }],
  'businessHours.end': [{ validator: validateBusinessHours, trigger: 'change' }],
  'description': [{ required: true, message: '请输入公司简介', trigger: 'blur' }],
}

function handleLogoChange(file: any) {
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    formData.value.logo = e.target?.result as string
  }
  reader.readAsDataURL(file.raw)
}

async function saveSettings() {
  if (!formRef.value)
    return

  try {
    loading.value = true
    await formRef.value.validate()

    // TODO: 调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('保存成功')
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
  finally {
    loading.value = false
  }
}
</script>

<style scoped>
.company-settings {
  padding: 20px;
}

.avatar-uploader {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  width: 100px;
  height: 100px;
  font-size: 28px;
  line-height: 100px;
  color: #8c939d;
  text-align: center;
}

.avatar {
  display: block;
  width: 100px;
  height: 100px;
}

.mx-2 {
  margin: 0 8px;
}

.mb-0 {
  margin-bottom: 0;
}
</style>
