<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 py-6">
      <!-- 面包屑导航 -->
      <div class="mb-4 flex items-center gap-2 text-gray-500">
        <span>主页</span>
        <span>/</span>
        <span>家政服务</span>
        <span>/</span>
        <span class="text-gray-900">家政员详情</span>
      </div>

      <div class="rounded-lg bg-white p-6 shadow-sm">
        <!-- 内容区域 -->
        <div class="grid grid-cols-2 gap-6">
            <div class=" text-lg col-span-2">身份信息</div>
          <!-- 左侧：身份证照片 -->
          <div>
            <div class="mb-6">
              <div class="grid grid-cols-1 gap-4">
                <div class="aspect-[1.58/1] overflow-hidden rounded-lg border">
                  <el-upload
                    class="h-full w-full"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    accept=".jpg,.jpeg,.png"
                  >
                    <div class="flex h-full w-full flex-col items-center justify-center text-center">
                      <el-icon class="mb-2 text-2xl text-gray-400"><Plus /></el-icon>
                      <div class="text-sm text-gray-500">上传身份证正面照片</div>
                    </div>
                  </el-upload>
                </div>
              </div>
              <div class="mt-2 text-xs text-gray-400">支持jpg、jpeg、png格式，大小不超过2M</div>
            </div>
          </div>

          <!-- 右侧：身份信息 -->
          <div>
            <div class="mb-6">
              <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">身份证号：</span>
                  <span>{{ housekeeperInfo?.idCardNo }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">姓名：</span>
                  <span>{{ housekeeperInfo?.name }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">生日：</span>
                  <span>{{ housekeeperInfo?.gender === 'male' ? '男' : '女' }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">性别：</span>
                  <span>{{ housekeeperInfo?.gender === 'male' ? '男' : '女' }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">民族：</span>
                  <span>{{ housekeeperInfo?.ethnicity === 'han' ? '汉族' : '其他民族' }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">籍贯：</span>
                  <span>{{ housekeeperInfo?.nativePlace }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">电话号码：</span>
                  <span>{{ housekeeperInfo?.tel }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">身份证地址：</span>
                  <span>{{ housekeeperInfo?.tel }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 个人资料编辑表单 -->
        <div class="mb-6">
          <div class="mb-4 text-lg font-medium">个人资料</div>
          <el-form :model="housekeeperInfo" label-width="100px">
            <div class="grid grid-cols-2 gap-6">
              <!-- 左侧：头像和基本信息 -->
              <div>
                <el-form-item label="头像">
                  <el-upload
                    class="avatar-uploader"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    accept=".jpg,.jpeg,.png"
                  >
                    <div class="h-32 w-32 overflow-hidden rounded-lg border cursor-pointer hover:border-primary-500">
                      <img v-if="housekeeperInfo.avatar" :src="housekeeperInfo.avatar" class="h-full w-full object-cover">
                      <div v-else class="flex h-full w-full flex-col items-center justify-center bg-gray-50">
                        <el-icon class="mb-2 text-2xl text-gray-400"><Plus /></el-icon>
                        <div class="text-sm text-gray-500">上传头像</div>
                      </div>
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item label="学历">
                  <el-select v-model="housekeeperInfo.education" placeholder="请选择学历">
                    <el-option label="无学历" value="无学历" />
                    <el-option label="小学" value="小学" />
                    <el-option label="初中" value="初中" />
                    <el-option label="高中" value="高中" />
                    <el-option label="大专" value="大专" />
                    <el-option label="本科" value="本科" />
                    <el-option label="研究生及以上" value="研究生及以上" />
                  </el-select>
                </el-form-item>
                <el-form-item label="婚姻状况">
                  <el-select v-model="housekeeperInfo.maritalStatus" placeholder="请选择婚姻状况">
                    <el-option label="未婚" value="未婚" />
                    <el-option label="已婚" value="已婚" />
                    <el-option label="离异" value="离异" />
                    <el-option label="丧偶" value="丧偶" />
                  </el-select>
                </el-form-item>
                <el-form-item label="属相">
                  <el-select v-model="housekeeperInfo.zodiac" placeholder="请选择属相">
                    <el-option label="鼠" value="鼠" />
                    <el-option label="牛" value="牛" />
                    <el-option label="虎" value="虎" />
                    <el-option label="兔" value="兔" />
                    <el-option label="龙" value="龙" />
                    <el-option label="蛇" value="蛇" />
                    <el-option label="马" value="马" />
                    <el-option label="羊" value="羊" />
                    <el-option label="猴" value="猴" />
                    <el-option label="鸡" value="鸡" />
                    <el-option label="狗" value="狗" />
                    <el-option label="猪" value="猪" />
                  </el-select>
                </el-form-item>
              </div>
              <!-- 右侧：其他信息 -->
              <div>
                <el-form-item label="星座">
                  <el-select v-model="housekeeperInfo.constellation" placeholder="请选择星座">
                    <el-option label="白羊座" value="白羊座" />
                    <el-option label="金牛座" value="金牛座" />
                    <el-option label="双子座" value="双子座" />
                    <el-option label="巨蟹座" value="巨蟹座" />
                    <el-option label="狮子座" value="狮子座" />
                    <el-option label="处女座" value="处女座" />
                    <el-option label="天秤座" value="天秤座" />
                    <el-option label="天蝎座" value="天蝎座" />
                    <el-option label="射手座" value="射手座" />
                    <el-option label="摩羯座" value="摩羯座" />
                    <el-option label="水瓶座" value="水瓶座" />
                    <el-option label="双鱼座" value="双鱼座" />
                  </el-select>
                </el-form-item>
                <el-form-item label="身高">
                  <el-input-number v-model="housekeeperInfo.height" :min="140" :max="200" :step="1" placeholder="请输入身高(cm)" />
                </el-form-item>
                <el-form-item label="体重">
                  <el-input-number v-model="housekeeperInfo.weight" :min="30" :max="150" :step="1" placeholder="请输入体重(kg)" />
                </el-form-item>
                <el-form-item label="紧急电话">
                  <el-input v-model="housekeeperInfo.emergencyContact" placeholder="请输入紧急联系人电话" />
                </el-form-item>
              </div>
            </div>
            <el-form-item label="自我介绍">
              <el-input v-model="housekeeperInfo.introduction" type="textarea" :rows="4" placeholder="请输入自我介绍" />
            </el-form-item>
            <el-form-item label="内部备注">
              <el-input v-model="housekeeperInfo.internalNote" type="textarea" :rows="4" placeholder="请输入内部备注信息" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary">保存修改</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="grid grid-cols-1 gap-6">
          <!-- 左侧：基本信息 -->
          <div>
            <!-- 照片/报告 -->
            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">照片/报告</div>
              <div class="mb-4">
                <div class="mb-2 text-gray-500">（仅支持上传jpg/jpeg/png/mp4格式的文件，按住图片可以拖动排序）</div>
                <div class="flex flex-wrap gap-4">
                  <el-button type="primary" plain>
                    <el-icon class="mr-1"><Plus /></el-icon>月子餐照片
                  </el-button>
                  <el-button type="primary" plain>
                    <el-icon class="mr-1"><Plus /></el-icon>体检报告
                  </el-button>
                  <el-button type="primary" plain>
                    <el-icon class="mr-1"><Plus /></el-icon>辅食照片
                  </el-button>
                  <el-button type="primary" plain>
                    <el-icon class="mr-1"><Plus /></el-icon>做饭厨艺照片
                  </el-button>
                  <el-button type="primary" plain>
                    <el-icon class="mr-1"><Plus /></el-icon>客户好评
                  </el-button>
                </div>
              </div>
              <div class="mb-4">
                <div class="flex items-center justify-between">
                  <div class="text-gray-500">工作生活照</div>
                  <el-switch v-model="housekeeperInfo.showWorkingPhoto" />
                </div>
                <div class="mt-4 grid grid-cols-4 gap-4">
                  <div v-for="photo in housekeeperInfo?.workingPhotos" :key="photo.id" class="relative aspect-square overflow-hidden rounded-lg border">
                    <img :src="photo.url" class="h-full w-full object-cover">
                    <div class="absolute bottom-0 left-0 right-0 bg-black/50 p-2 text-center text-white">
                      {{ photo.description }}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div class="flex items-center justify-between">
                  <div class="text-gray-500">资格证照片</div>
                  <el-switch v-model="housekeeperInfo.showCertificatePhoto" />
                </div>
                <div class="mt-4 grid grid-cols-4 gap-4">
                  <div v-for="cert in housekeeperInfo?.certificatePhotos" :key="cert.id" class="relative aspect-square overflow-hidden rounded-lg border">
                    <img :src="cert.url" class="h-full w-full object-cover">
                    <div class="absolute bottom-0 left-0 right-0 bg-black/50 p-2 text-center text-white">
                      {{ cert.name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">业务信息</div>
              <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">类型：</span>
                  <span>{{ housekeeperInfo?.type }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">工作状态：</span>
                  <el-tag :type="housekeeperInfo?.statusType">{{ housekeeperInfo?.status }}</el-tag>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">工作经验：</span>
                  <span>{{ housekeeperInfo?.experience }}年</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">住家：</span>
                  <span>{{ housekeeperInfo?.canLiveIn ? '可以' : '不可以' }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">月子数：</span>
                  <span>{{ housekeeperInfo?.monthlyCount || 0 }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">入行年份：</span>
                  <span>{{ housekeeperInfo?.startYear }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">合同档案：</span>
                  <el-switch v-model="housekeeperInfo.showContract" />
                </div>
                <div class="flex items-center gap-4">
                  <span class="w-24 text-gray-500">来源：</span>
                  <div class="flex items-center gap-2">
                    <el-tag v-for="source in housekeeperInfo?.sources" :key="source" size="small" class="mr-1">{{ source }}</el-tag>
                    <el-button link type="primary" size="small">管理</el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 家庭成员 -->
            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">家庭成员</div>
              <div class="space-y-4">
                <div v-for="member in housekeeperInfo?.familyMembers" :key="member.id" class="rounded-lg border p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                      <div class="text-lg">{{ member.name }}</div>
                      <div class="text-gray-500">{{ member.relation }}</div>
                    </div>
                    <div class="text-gray-500">{{ member.age }}岁</div>
                  </div>
                  <div class="mt-2 text-gray-500">{{ member.occupation }}</div>
                </div>
                <el-button type="primary" plain class="w-full">
                  <el-icon class="mr-1"><Plus /></el-icon>添加家庭成员
                </el-button>
              </div>
            </div>

            <!-- 工作经历 -->
            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">工作经历</div>
              <div class="space-y-4">
                <div v-for="work in housekeeperInfo?.workHistory" :key="work.id" class="rounded-lg border p-4">
                  <div class="flex items-center justify-between">
                    <div class="text-lg font-medium">{{ work.employer }}</div>
                    <div class="text-gray-500">{{ work.period }}</div>
                  </div>
                  <div class="mt-2 text-gray-500">{{ work.position }}</div>
                  <div class="mt-2 text-gray-600">{{ work.description }}</div>
                </div>
                <el-button type="primary" plain class="w-full">
                  <el-icon class="mr-1"><Plus /></el-icon>添加工作经历
                </el-button>
              </div>
            </div>

            <!-- 培训记录 -->
            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">培训记录</div>
              <div class="space-y-4">
                <div v-for="training in housekeeperInfo?.trainingRecords" :key="training.id" class="rounded-lg border p-4">
                  <div class="flex items-center justify-between">
                    <div class="text-lg font-medium">{{ training.course }}</div>
                    <div class="text-gray-500">{{ training.date }}</div>
                  </div>
                  <div class="mt-2 text-gray-500">{{ training.institution }}</div>
                  <div class="mt-2 text-gray-600">{{ training.description }}</div>
                </div>
                <el-button type="primary" plain class="w-full">
                  <el-icon class="mr-1"><Plus /></el-icon>添加培训记录
                </el-button>
              </div>
            </div>
          </div>

          <!-- 右侧：技能和证书 -->
          <div>
            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">技能特长</div>
              <div class="flex flex-wrap gap-2">
                <el-tag v-for="skill in housekeeperInfo?.skills" :key="skill" size="large">
                  {{ skill }}
                </el-tag>
              </div>
            </div>

            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">证书资质</div>
              <div class="grid grid-cols-3 gap-4">
                <div v-for="cert in housekeeperInfo?.certificates" :key="cert.id" class="overflow-hidden rounded-lg border">
                  <img :src="cert.image" class="h-32 w-full object-cover">
                  <div class="p-2 text-center">{{ cert.name }}</div>
                </div>
              </div>
            </div>

            <div class="mb-6">
              <div class="mb-4 text-lg font-medium">自我介绍</div>
              <div class="rounded-lg bg-gray-50 p-4 text-gray-600">
                {{ housekeeperInfo?.introduction || '暂无自我介绍' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const housekeeperInfo = {
  avatar: '/public/LOGO金刚(圆).png',
  name: '张阿姨',
  age: 45,
  experience: 10,
  idCardNo: '330************123',
  gender: 'female',
  ethnicity: 'han',
  nativePlace: '浙江杭州',
  tel: '138****1234',
  education: '高中',
  maritalStatus: '已婚',
  zodiac: '龙',
  constellation: '天秤座',
  height: 165,
  weight: 60,
  type: '月嫂',
  status: '空闲',
  statusType: 'success',
  canLiveIn: true,
  monthlyCount: 5,
  startYear: 2015,
  showContract: true,
  showWorkingPhoto: true,
  showCertificatePhoto: true,
  sources: ['平台注册', '门店推荐'],
  workingPhotos: [
    { id: 1, url: '/public/LOGO金刚(圆).png', description: '工作照片1' },
    { id: 2, url: '/public/LOGO金刚(圆).png', description: '工作照片2' }
  ],
  certificatePhotos: [
    { id: 1, url: '/public/LOGO金刚(圆).png', name: '月嫂证书' },
    { id: 2, url: '/public/LOGO金刚(圆).png', name: '健康证' }
  ],
  familyMembers: [
    { id: 1, name: '张先生', relation: '配偶', age: 47, occupation: '工人' },
    { id: 2, name: '张小明', relation: '儿子', age: 20, occupation: '大学生' }
  ],
  workHistory: [
    {
      id: 1,
      employer: '王女士家',
      period: '2022.01-2022.03',
      position: '月嫂',
      description: '照顾新生儿及产妇，获得雇主好评'
    },
    {
      id: 2,
      employer: '李女士家',
      period: '2021.10-2021.12',
      position: '月嫂',
      description: '负责新生儿护理和月子餐制作'
    }
  ]
}
</script>