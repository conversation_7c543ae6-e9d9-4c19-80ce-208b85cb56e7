<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 py-6">
      <!-- 搜索栏 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 flex flex-wrap gap-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">导入时间</span>
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-80"
            />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">数据类型</span>
            <el-select v-model="searchForm.type" placeholder="所有类型" class="w-32">
              <el-option label="家政员简历" value="resume" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">录入门店</span>
            <el-select v-model="searchForm.store" placeholder="所有门店" class="w-48">
              <el-option label="厦门小羽佳线索店" value="store1" />
            </el-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">状态</span>
            <el-select v-model="searchForm.status" placeholder="所有状态" class="w-32">
              <el-option label="导入成功" value="success" />
              <el-option label="导入失败" value="failed" />
              <el-option label="导入中" value="processing" />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <el-table :data="tableData" style="width: 100%;">
          <el-table-column prop="type" label="数据类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fileName" label="文件名" width="200" />
          <el-table-column prop="store" label="录入门店" width="180" />
          <el-table-column prop="account" label="数据归属" width="180" />
          <el-table-column prop="total" label="数据总量" width="100" align="center" />
          <el-table-column prop="success" label="成功数" width="100" align="center">
            <template #default="{ row }">
              <span class="text-success-500">{{ row.success }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="failed" label="失败数" width="100" align="center">
            <template #default="{ row }">
              <span class="text-danger-500">{{ row.failed }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.statusType" size="small">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="creator" label="操作人" width="120" />
          <el-table-column prop="createTime" label="导入时间" width="180" />
          <el-table-column label="操作" fixed="right" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewDetail(row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="mt-4 flex justify-end">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 搜索表单
const searchForm = reactive({
  dateRange: [],
  type: '',
  store: '',
  status: ''
})

// 表格数据
const tableData = ref([
  {
    type: '家政员简历',
    fileName: '家政员简历导入模板.xlsx',
    store: '厦门小羽佳线索店',
    account: '厦门小羽佳线索店',
    total: 100,
    success: 95,
    failed: 5,
    status: '导入成功',
    statusType: 'success',
    creator: '张三',
    createTime: '2024-01-18 10:30:00'
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 查看详情
const handleViewDetail = (row: any) => {
  console.log('查看详情', row)
}
</script>